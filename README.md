# IoTeX-Solana 套利系统

一个自动化的跨链套利系统，用于在 IoTeX (MIMO DEX) 和 Solana (CEX) 之间进行 SOL/IOTX 套利交易。

## 功能特性

- 🔍 **实时价格监控**：监控 CEX 和 DEX 之间的价格差异
- 📊 **智能套利分析**：基于实际兑换率计算套利机会
- 🤖 **自动化执行**：支持自动执行套利交易
- 💰 **费用优化**：考虑 MIMO v3 的 0.3% 交易费用
- 🔐 **安全可靠**：支持测试模式和生产模式
- 📈 **详细日志**：完整的交易和分析日志

## 系统架构

```
CEX (Binance) ←→ IoTeX (MIMO DEX) ←→ Solana
     ↓                ↓                ↓
  SOL 价格        SOL/IOTX 兑换      SOL 跨链
```

## 快速开始

### 1. 环境配置

创建 `.env` 文件：

```bash
# 币安 API 配置
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET=your_binance_api_secret

# IoTeX 配置
IOTEX_RPC_URL=https://babel-api.mainnet.iotex.io
IOTEX_PRIVATE_KEY=your_iotex_private_key
IOTEX_WALLET_ADDRESS=your_iotex_wallet_address

# Solana 配置
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_PRIVATE_KEY=your_solana_private_key
SOLANA_WALLET_ADDRESS=your_solana_wallet_address

# MIMO 数据库配置
MIMO_DB_URL=postgresql://mimo:password@host:port/mimo

# 套利配置
ARBITRAGE_MIN_IOTX_DIFFERENCE=50
ARBITRAGE_MAX_TRADE_AMOUNT=100
ARBITRAGE_SLIPPAGE=1

# 运行环境
NODE_ENV=development  # 或 production

# 更多配置选项请参考 .env.example 文件
```

### 2. 安装依赖

```bash
npm install
```

### 3. 生成钱包（可选）

如果你还没有钱包，可以使用内置的钱包生成器：

```bash
# 生成钱包并保存到文件
npm run generate-wallets-save

# 测试钱包生成器
npm run test-wallet-generator
```

### 4. 运行系统

#### 单次分析模式

```bash
# 执行一次套利分析
npm run start:analyze

# 或者
npm start -- --analyze
```

#### 监控模式

```bash
# 启动持续监控（默认30秒间隔）
npm run start:monitor

# 自定义监控间隔（60秒）
npm start -- --monitor --interval=60000
```

#### 原始 POC 模式

```bash
# 运行原始的 POC 脚本
npm run start:poc
```

## 使用说明

### 套利逻辑

系统比较 CEX 和 DEX 上 SOL 可以换多少 IOTX：

1. **CEX 兑换率**：`SOL 价格 / IOTX 价格 = 每 SOL 可换多少 IOTX`
2. **DEX 兑换率**：通过 MIMO API 获取实际的 `SOL → IOTX` 兑换率
3. **差异计算**：`|CEX 兑换率 - DEX 兑换率|`
4. **费用扣除**：扣除 MIMO v3 的 0.3% 交易费用
5. **盈利判断**：调整后差异 > 50 IOTX 即为有利可图

### 套利方向

#### CEX → DEX 套利
当 CEX 兑换率 > DEX 兑换率时：
1. 在 CEX 买 SOL
2. 跨链到 IoTeX
3. 在 MIMO 换成 IOTX
4. 获得更多 IOTX

#### DEX → CEX 套利
当 DEX 兑换率 > CEX 兑换率时：
1. 在 MIMO 用 IOTX 换 SOL
2. 跨链到 Solana
3. 在 CEX 卖 SOL
4. 获得更多 USDT

### 输出示例

```
🔍 执行套利分析...

📊 套利分析结果:
CEX SOL 价格: $181.52
IOTX 价格: $0.0281
CEX 兑换率: 1 SOL = 6451.60 IOTX
DEX 兑换率: 1 SOL = 6466.62 IOTX
原始差异: 15.02 IOTX
调整后差异: -4.38 IOTX
套利方向: dex_to_cex

❌ 当前没有盈利的套利机会
差异: -4.38 IOTX (需要 > 50 IOTX)
```

## 安全注意事项

### ⚠️ 重要提醒

1. **测试环境**：
   - 默认运行在测试模式，不会执行真实交易
   - 设置 `NODE_ENV=production` 才会执行真实交易

2. **私钥安全**：
   - 不要将私钥提交到版本控制系统
   - 使用环境变量或安全的密钥管理系统

3. **资金风险**：
   - 套利交易存在市场风险
   - 建议先用小额资金测试
   - 确保理解所有费用和风险

4. **API 限制**：
   - 注意 CEX API 的调用频率限制
   - 监控间隔不要设置得太短

## 配置参数

### 套利配置

```typescript
arbitrage: {
  minIotxDifference: 50,    // 最小 IOTX 差异要求
  maxTradeAmount: 100,      // 最大交易金额 $100
  slippage: 1,              // 1% 滑点容忍度
}
```

### 监控配置

- **默认间隔**：30 秒
- **自定义间隔**：使用 `--interval=毫秒数` 参数
- **最小建议间隔**：10 秒（避免 API 限制）

## 故障排除

### 常见问题

1. **API 连接失败**：
   ```bash
   # 检查网络连接和 API 配置
   npm run start:analyze
   ```

2. **数据库连接失败**：
   ```bash
   # 检查 MIMO_DB_URL 配置
   echo $MIMO_DB_URL
   ```

3. **钱包未初始化**：
   ```bash
   # 检查私钥配置
   echo $IOTEX_PRIVATE_KEY
   echo $SOLANA_PRIVATE_KEY
   ```

### 调试模式

```bash
# 启用详细日志
DEBUG=* npm start

# 只显示套利相关日志
DEBUG=arbitrage:* npm start
```

## 开发说明

### 项目结构

```
├── start.ts              # 主启动脚本
├── poc.ts                # 原始 POC 脚本
├── arbitrage.ts          # 套利服务
├── cex.ts                # CEX 服务
├── mimo.ts               # MIMO DEX 服务
├── iotube-bridge.ts      # 跨链桥服务
├── solana.ts             # Solana 服务
├── scripts/              # 工具脚本
│   ├── generate-wallets.ts
│   └── test-wallet-generator.ts
└── docs/                 # 文档
```

### 扩展功能

1. **通知系统**：添加邮件、微信、Telegram 通知
2. **风险管理**：添加止损、仓位管理
3. **多交易对**：支持更多代币对的套利
4. **历史数据**：记录和分析历史套利机会

## 许可证

MIT License - 请查看 LICENSE 文件了解详情。

## 免责声明

本软件仅供学习和研究使用。使用本软件进行实际交易的所有风险由用户自行承担。开发者不对任何损失负责。
