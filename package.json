{"name": "arb-sys", "module": "index.ts", "devDependencies": {"@types/bun": "latest", "ts-node": "^10.9.2"}, "peerDependencies": {"typescript": "^5"}, "private": true, "type": "module", "scripts": {"start": "bun run start.ts", "start:monitor": "ts-node start.ts --monitor", "start:analyze": "ts-node start.ts --analyze", "start:test": "bun run start.ts --test", "start:poc": "ts-node poc.ts", "generate-wallets": "ts-node scripts/generate-wallets.ts", "generate-wallets-save": "ts-node scripts/generate-wallets.ts --save --env", "test-wallet-generator": "ts-node scripts/test-wallet-generator.ts", "demo-wallet-generator": "ts-node scripts/demo.ts"}, "dependencies": {"@iotexproject/iotex-address-ts": "^1.0.4", "@solana/spl-token": "0.4.13", "@solana/web3.js": "1.98.0", "@types/node-fetch": "^2.6.12", "axios": "^1.6.2", "bignumber.js": "^9.3.1", "borsh": "0.7.0", "bs58": "^6.0.0", "ccxt": "^4.4.96", "dotenv": "^16.3.1", "ethers": "^6.15.0", "lodash": "^4.17.21", "node-fetch": "2.6.12", "postgres": "^3.4.3", "viem": "^1.19.9"}}