# IoTeX-Solana 套利系统环境变量配置

# ===========================================
# 币安 API 配置
# ===========================================
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# ===========================================
# IoTeX 网络配置
# ===========================================
IOTEX_RPC_URL=https://babel-api.mainnet.iotex.io
IOTEX_PRIVATE_KEY=your_iotex_private_key_here
IOTEX_WALLET_ADDRESS=your_iotex_wallet_address_here

# ===========================================
# Solana 网络配置
# ===========================================
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_PRIVATE_KEY=your_solana_private_key_here
SOLANA_WALLET_ADDRESS=your_solana_wallet_address_here

# ===========================================
# MIMO 数据库配置
# ===========================================
MIMO_DB_URL=postgresql://mimo:password@host:port/mimo

# ===========================================
# 系统配置
# ===========================================
NODE_ENV=development  # development 或 production
AUTO_EXECUTE=false    # 是否自动执行套利交易

# ===========================================
# 可选配置
# ===========================================
# DEBUG=arbitrage:*   # 启用调试日志
# LOG_LEVEL=info      # 日志级别
