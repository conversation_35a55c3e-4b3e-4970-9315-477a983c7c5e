# IoTeX-Solana 套利系统环境变量配置

# ===========================================
# 币安 API 配置
# ===========================================
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET=your_binance_api_secret_here
BINANCE_API_URL=https://api.binance.com/api/v3

# ===========================================
# IoTeX 网络配置
# ===========================================
IOTEX_RPC_URL=https://babel-api.mainnet.iotex.io
IOTEX_PRIVATE_KEY=your_iotex_private_key_here
IOTEX_WALLET_ADDRESS=your_iotex_wallet_address_here
IOTEX_CHAIN_ID=4689
IOTEX_CASHIER_ADDRESS=******************************************

# ===========================================
# Solana 网络配置
# ===========================================
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_PRIVATE_KEY=your_solana_private_key_here
SOLANA_WALLET_ADDRESS=your_solana_wallet_address_here
SOLANA_CHAIN_ID=101
SOLANA_BRIDGE_PROGRAM_ID=A9SGRcytnfx6U1QrnMUwK5sxYyCYY3MpyrPcyeafhSMF
SOLANA_BRIDGE_CONFIG=E83sFGG5R3psWhotZSihbPb6DLmLnGK122ZF9q7VmYSM

# ===========================================
# MIMO 数据库配置
# ===========================================
MIMO_DB_URL=postgresql://mimo:password@host:port/mimo
MIMO_ROUTER_V3=******************************************
MIMO_TRADE_API_URL=https://swap-api.mimo.exchange/api/trade
MIMO_FEE_RATE=0.003

# ===========================================
# 代币合约地址配置
# ===========================================
SOL_TOKEN_ADDRESS=******************************************
WIOTX_TOKEN_ADDRESS=******************************************
WSOL_TOKEN_ADDRESS=So11111111111111111111111111111111111111112
SOL_CTOKEN=********************************************

# ===========================================
# 套利配置
# ===========================================
ARBITRAGE_MIN_IOTX_DIFFERENCE=50
ARBITRAGE_MAX_TRADE_AMOUNT=100
ARBITRAGE_SLIPPAGE=1

# ===========================================
# 跨链桥配置
# ===========================================
BRIDGE_CONTRACT_ADDRESS=0x0000000000000000000000000000000000000000
BRIDGE_FEE_RATE=0.001
BRIDGE_FIXED_FEE=0.0001
MIN_TRANSFER_AMOUNT=0.001

# ===========================================
# 系统配置
# ===========================================
NODE_ENV=development  # development 或 production
AUTO_EXECUTE=false    # 是否自动执行套利交易

# 测试模式配置
# ===========================================
TEST_MODE=false           # 启用测试模式，忽略盈利条件
TEST_TRADE_AMOUNT=20      # 测试模式下的交易金额 ($)
TEST_CYCLE_INTERVAL=300   # 测试循环间隔 (秒)
TEST_MAX_CYCLES=10        # 最大测试循环次数 (0=无限循环)

# ===========================================
# 可选配置
# ===========================================
# BINANCE_SANDBOX=true  # 启用 Binance 沙盒模式
# DEBUG=arbitrage:*     # 启用调试日志
# LOG_LEVEL=info        # 日志级别
