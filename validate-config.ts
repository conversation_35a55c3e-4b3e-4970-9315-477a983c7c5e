#!/usr/bin/env ts-node

import dotenv from "dotenv";

// 加载环境变量
dotenv.config();

/**
 * 配置验证脚本
 * 检查所有必要的环境变量是否已正确设置
 */

interface ConfigValidation {
  key: string;
  required: boolean;
  description: string;
  sensitive?: boolean;
}

const configItems: ConfigValidation[] = [
  // 币安 API 配置
  { key: "BINANCE_API_KEY", required: true, description: "币安 API 密钥", sensitive: true },
  { key: "BINANCE_SECRET", required: true, description: "币安 API 密钥", sensitive: true },
  { key: "BINANCE_API_URL", required: false, description: "币安 API 基础 URL" },
  { key: "BINANCE_SANDBOX", required: false, description: "币安沙盒模式" },

  // IoTeX 网络配置
  { key: "IOTEX_RPC_URL", required: false, description: "IoTeX RPC 节点地址" },
  { key: "IOTEX_CHAIN_ID", required: false, description: "IoTeX 链 ID" },
  { key: "IOTEX_PRIVATE_KEY", required: true, description: "IoTeX 私钥", sensitive: true },
  { key: "IOTEX_WALLET_ADDRESS", required: true, description: "IoTeX 钱包地址" },
  { key: "IOTEX_CASHIER_ADDRESS", required: false, description: "IoTeX Cashier 合约地址" },

  // Solana 网络配置
  { key: "SOLANA_RPC_URL", required: false, description: "Solana RPC 节点地址" },
  { key: "SOLANA_CHAIN_ID", required: false, description: "Solana 链 ID" },
  { key: "SOLANA_PRIVATE_KEY", required: true, description: "Solana 私钥", sensitive: true },
  { key: "SOLANA_WALLET_ADDRESS", required: true, description: "Solana 钱包地址" },
  { key: "SOLANA_BRIDGE_PROGRAM_ID", required: false, description: "Solana 桥接程序 ID" },
  { key: "SOLANA_BRIDGE_CONFIG", required: false, description: "Solana 桥接配置地址" },

  // MIMO 配置
  { key: "MIMO_DB_URL", required: true, description: "MIMO 数据库连接字符串", sensitive: true },
  { key: "MIMO_ROUTER_V3", required: false, description: "MIMO V3 路由器合约地址" },
  { key: "MIMO_TRADE_API_URL", required: false, description: "MIMO 交易 API 地址" },
  { key: "MIMO_FEE_RATE", required: false, description: "MIMO 交易手续费率" },

  // 代币合约地址
  { key: "SOL_TOKEN_ADDRESS", required: false, description: "IoTeX 上的 SOL 代币地址" },
  { key: "WIOTX_TOKEN_ADDRESS", required: false, description: "WIOTX 代币地址" },
  { key: "WSOL_TOKEN_ADDRESS", required: false, description: "Solana 上的 WSOL 地址" },
  { key: "SOL_CTOKEN", required: false, description: "Solana 桥接中的 SOL cToken 地址" },

  // 跨链桥配置
  { key: "BRIDGE_CONTRACT_ADDRESS", required: false, description: "桥接合约地址" },
  { key: "BRIDGE_FEE_RATE", required: false, description: "桥接手续费率" },
  { key: "BRIDGE_FIXED_FEE", required: false, description: "桥接固定手续费" },
  { key: "MIN_TRANSFER_AMOUNT", required: false, description: "最小转账金额" },

  // 套利配置
  { key: "ARBITRAGE_MIN_IOTX_DIFFERENCE", required: false, description: "最小 IOTX 差异要求" },
  { key: "ARBITRAGE_MAX_TRADE_AMOUNT", required: false, description: "最大交易金额" },
  { key: "ARBITRAGE_SLIPPAGE", required: false, description: "滑点容忍度" },

  // 系统配置
  { key: "NODE_ENV", required: false, description: "运行环境" },
];

function validateConfig() {
  console.log("🔍 验证环境变量配置...\n");

  let hasErrors = false;
  let hasWarnings = false;

  const results = {
    required: [] as string[],
    optional: [] as string[],
    missing: [] as string[],
    warnings: [] as string[],
  };

  for (const item of configItems) {
    const value = process.env[item.key];
    const hasValue = value !== undefined && value !== "";

    if (item.required) {
      if (hasValue) {
        results.required.push(item.key);
        const displayValue = item.sensitive ? "***" : value;
        console.log(`✅ ${item.key}: ${displayValue} (${item.description})`);
      } else {
        results.missing.push(item.key);
        console.log(`❌ ${item.key}: 未设置 (${item.description}) - 必需`);
        hasErrors = true;
      }
    } else {
      if (hasValue) {
        results.optional.push(item.key);
        const displayValue = item.sensitive ? "***" : value;
        console.log(`✅ ${item.key}: ${displayValue} (${item.description})`);
      } else {
        results.warnings.push(item.key);
        console.log(`⚠️  ${item.key}: 使用默认值 (${item.description})`);
        hasWarnings = true;
      }
    }
  }

  console.log("\n" + "=".repeat(60));
  console.log("📊 配置验证结果:");
  console.log(`✅ 必需配置项: ${results.required.length}/${configItems.filter(i => i.required).length}`);
  console.log(`✅ 可选配置项: ${results.optional.length}/${configItems.filter(i => !i.required).length}`);
  
  if (results.missing.length > 0) {
    console.log(`❌ 缺失必需配置: ${results.missing.length}`);
    console.log("   缺失项:", results.missing.join(", "));
  }
  
  if (results.warnings.length > 0) {
    console.log(`⚠️  使用默认值: ${results.warnings.length}`);
  }

  console.log("\n" + "=".repeat(60));
  
  if (hasErrors) {
    console.log("❌ 配置验证失败！请设置所有必需的环境变量。");
    console.log("💡 提示：复制 .env.example 到 .env 并填入实际值");
    process.exit(1);
  } else if (hasWarnings) {
    console.log("⚠️  配置验证通过，但有些配置项使用默认值。");
    console.log("💡 建议：检查 .env.example 文件了解所有可配置项");
  } else {
    console.log("✅ 所有配置验证通过！");
  }
}

// 运行验证
if (require.main === module) {
  validateConfig();
}

export { validateConfig };
