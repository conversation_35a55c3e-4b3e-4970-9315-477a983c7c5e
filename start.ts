#!/usr/bin/env ts-node

import { ArbitrageService } from "./arbitrage";
import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube-bridge";
import { SolanaService } from "./solana";

/**
 * 完整的套利系统启动脚本
 * 连接所有服务，提供完整的套利链路
 */

// 配置信息
const config = {
  binance: {
    apiKey: process.env.BINANCE_API_KEY || "",
    secret: process.env.BINANCE_API_SECRET || "",
    sandbox: false, // 生产环境设置为 false
  },
  mimo: {
    dbUrl: process.env.MIMO_DB_URL || "postgresql://mimo:mimo@localhost:5432/mimo",
    rpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    privateKey: process.env.IOTEX_PRIVATE_KEY || "",
  },
  bridge: {
    iotexRpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    solanaRpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    iotexPrivateKey: process.env.IOTEX_PRIVATE_KEY || "",
    solanaPrivateKey: process.env.SOLANA_PRIVATE_KEY || "",
  },
  solana: {
    rpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    privateKey: process.env.SOLANA_PRIVATE_KEY || "",
  },
  arbitrage: {
    minIotxDifference: 50, // 最小 IOTX 差异要求
    maxTradeAmount: 100, // 最大交易金额 $100
    slippage: 1, // 1% 滑点容忍度
    walletAddresses: {
      iotex: process.env.IOTEX_WALLET_ADDRESS || "",
      solana: process.env.SOLANA_WALLET_ADDRESS || "",
    },
  },
};

// 全局服务实例
let arbitrageService: ArbitrageService;
let cexService: CexService;
let mimoService: MimoService;
let bridgeService: IoTubeBridgeService;
let solanaService: SolanaService;

/**
 * 初始化所有服务
 */
async function initializeServices() {
  console.log("🔧 初始化服务...");

  try {
    // 检查必要的环境变量
    if (!config.binance.apiKey || !config.binance.secret) {
      throw new Error("缺少币安 API 配置，请设置 BINANCE_API_KEY 和 BINANCE_API_SECRET");
    }

    // 初始化服务
    cexService = new CexService(config.binance);
    mimoService = new MimoService(config.mimo.dbUrl, {
      rpcUrl: config.mimo.rpcUrl,
      privateKey: config.mimo.privateKey,
    });
    bridgeService = new IoTubeBridgeService(config.bridge);
    solanaService = new SolanaService(config.solana);

    arbitrageService = new ArbitrageService(
      cexService,
      mimoService,
      bridgeService,
      solanaService,
      config.arbitrage
    );

    console.log("✅ 所有服务初始化完成");
    return true;
  } catch (error) {
    console.error("❌ 服务初始化失败:", error);
    return false;
  }
}

/**
 * 测试服务连接
 */
async function testConnections() {
  console.log("🧪 测试服务连接...");

  try {
    // 测试 CEX 连接
    console.log("测试 CEX 连接...");
    const solPrice = await cexService.getSolPrice();
    const iotxPrice = await cexService.getIotxPrice();
    console.log(`✅ CEX 连接正常 - SOL: $${solPrice}, IOTX: $${iotxPrice}`);

    // 测试 MIMO 连接
    console.log("测试 MIMO 连接...");
    const dexRate = await mimoService.getSolToIotxRate(1);
    console.log(`✅ MIMO 连接正常 - 1 SOL = ${dexRate.toFixed(2)} IOTX`);

    return true;
  } catch (error) {
    console.error("❌ 服务连接测试失败:", error);
    return false;
  }
}

/**
 * 执行单次套利分析
 */
async function analyzeSingleOpportunity() {
  console.log("🔍 执行套利分析...");

  try {
    const opportunity = await arbitrageService.analyzeArbitrageOpportunity();

    console.log("\n📊 套利分析结果:");
    console.log(`CEX SOL 价格: $${opportunity.cexSolPrice.toFixed(2)}`);
    console.log(`IOTX 价格: $${opportunity.iotxPrice.toFixed(4)}`);
    console.log(`CEX 兑换率: 1 SOL = ${opportunity.cexSolToIotxRate.toFixed(2)} IOTX`);
    console.log(`DEX 兑换率: 1 SOL = ${opportunity.dexSolToIotxRate.toFixed(2)} IOTX`);
    console.log(`原始差异: ${opportunity.rateDifference.toFixed(2)} IOTX`);
    console.log(`调整后差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX`);
    console.log(`套利方向: ${opportunity.direction}`);

    if (opportunity.profitable) {
      console.log(`\n✅ 发现套利机会! 差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX`);
      console.log(`💰 预估收益: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX`);
      
      return opportunity;
    } else {
      console.log(`\n❌ 当前没有盈利的套利机会`);
      console.log(`差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX (需要 > 50 IOTX)`);
      
      return null;
    }
  } catch (error) {
    console.error("❌ 套利分析失败:", error);
    return null;
  }
}

/**
 * 启动监控模式
 */
async function startMonitoring(intervalMs: number = 30000) {
  console.log(`🔄 启动套利监控模式，检查间隔: ${intervalMs / 1000}秒`);

  // 立即执行一次分析
  await analyzeSingleOpportunity();

  // 开始定期监控
  setInterval(async () => {
    try {
      console.log(`\n⏰ ${new Date().toLocaleString()} - 检查套利机会...`);
      const opportunity = await analyzeSingleOpportunity();
      
      if (opportunity) {
        console.log("🚨 发现套利机会！");
        // 这里可以添加通知逻辑，比如发送邮件、微信通知等
        // await sendNotification(opportunity);
        
        // 如果配置了自动执行，可以在这里执行套利
        // if (process.env.AUTO_EXECUTE === 'true') {
        //   await executeArbitrage(opportunity);
        // }
      }
    } catch (error) {
      console.error("监控过程中出错:", error);
    }
  }, intervalMs);
}

/**
 * 执行套利交易
 */
async function executeArbitrage(opportunity: any) {
  console.log("🚀 开始执行套利交易...");
  
  try {
    // 安全检查
    if (!mimoService.isWalletInitialized()) {
      throw new Error("钱包未初始化，无法执行交易");
    }

    if (process.env.NODE_ENV !== "production") {
      console.log("💡 非生产环境，跳过实际交易执行");
      return;
    }

    // 执行套利
    const tradeAmount = Math.min(50, config.arbitrage.maxTradeAmount); // 限制交易金额
    const result = await arbitrageService.executeArbitrage(tradeAmount);
    
    if (result) {
      console.log("🎉 套利执行完成:", result);
    } else {
      console.log("❌ 套利执行失败");
    }
  } catch (error) {
    console.error("❌ 套利执行出错:", error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log("🚀 启动套利系统...");
  console.log("配置信息:", {
    最小IOTX差异: `${config.arbitrage.minIotxDifference} IOTX`,
    最大交易金额: `$${config.arbitrage.maxTradeAmount}`,
    环境: process.env.NODE_ENV || 'development',
  });

  try {
    // 1. 初始化服务
    const initSuccess = await initializeServices();
    if (!initSuccess) {
      process.exit(1);
    }

    // 2. 测试连接
    const testSuccess = await testConnections();
    if (!testSuccess) {
      console.log("⚠️ 服务连接测试失败，但继续运行...");
    }

    // 3. 根据命令行参数决定运行模式
    const args = process.argv.slice(2);
    
    if (args.includes('--monitor') || args.includes('-m')) {
      // 监控模式
      const interval = parseInt(args.find(arg => arg.startsWith('--interval='))?.split('=')[1] || '30000');
      await startMonitoring(interval);
    } else if (args.includes('--analyze') || args.includes('-a')) {
      // 单次分析模式
      await analyzeSingleOpportunity();
      process.exit(0);
    } else {
      // 默认：单次分析
      console.log("💡 使用 --monitor 启动监控模式，--analyze 进行单次分析");
      await analyzeSingleOpportunity();
      process.exit(0);
    }

  } catch (error) {
    console.error("❌ 系统启动失败:", error);
    process.exit(1);
  } finally {
    // 清理资源
    if (mimoService) {
      await mimoService.close();
    }
  }
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n🛑 收到退出信号，正在清理资源...');
  if (mimoService) {
    await mimoService.close();
  }
  process.exit(0);
});

// 启动程序
if (require.main === module) {
  main();
}

export { main, startMonitoring, analyzeSingleOpportunity, initializeServices };
