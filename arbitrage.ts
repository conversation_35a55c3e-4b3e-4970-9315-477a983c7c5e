import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube-bridge";
import { SolanaService } from "./solana";
// @ts-ignore
import { from } from "@iotexproject/iotex-address-ts";

/**
 * 套利策略模块
 * 协调 CEX、MIMO 和 IoTube Bridge 服务执行套利策略
 */
export class ArbitrageService {
  private cexService: CexService;
  private mimoService: MimoService;
  private bridgeService: IoTubeBridgeService;
  private solanaService: SolanaService;
  private config: {
    minIotxDifference: number;
    maxTradeAmount: number;
    walletAddresses: {
      iotex: string;
      solana: string;
    };
  };

  // 执行状态管理
  private isExecuting: boolean = false;
  private executionStartTime: number | null = null;
  private currentOperation: string | null = null;

  constructor(
    cexService: CexService,
    mimoService: MimoService,
    bridgeService: IoTubeBridgeService,
    solanaService: SolanaService,
    config: {
      minIotxDifference: number;
      maxTradeAmount: number;
      walletAddresses: {
        iotex: string;
        solana: string;
      };
    }
  ) {
    this.cexService = cexService;
    this.mimoService = mimoService;
    this.bridgeService = bridgeService;
    this.solanaService = solanaService;
    this.config = config;
  }

  /**
   * 检查是否正在执行套利
   */
  isArbitrageExecuting(): boolean {
    return this.isExecuting;
  }

  /**
   * 获取当前执行状态信息
   */
  getExecutionStatus() {
    return {
      isExecuting: this.isExecuting,
      executionStartTime: this.executionStartTime,
      currentOperation: this.currentOperation,
      executionDuration: this.executionStartTime ? Date.now() - this.executionStartTime : 0
    };
  }

  /**
   * 开始执行状态
   */
  private startExecution(operation: string) {
    this.isExecuting = true;
    this.executionStartTime = Date.now();
    this.currentOperation = operation;
    console.log(`🚀 开始执行: ${operation}`);
  }

  /**
   * 结束执行状态
   */
  private endExecution() {
    const duration = this.executionStartTime ? Date.now() - this.executionStartTime : 0;
    console.log(`✅ 执行完成: ${this.currentOperation}, 耗时: ${duration}ms`);

    this.isExecuting = false;
    this.executionStartTime = null;
    this.currentOperation = null;
  }

  /**
   * 执行失败时的状态处理
   */
  private failExecution(error: any) {
    const duration = this.executionStartTime ? Date.now() - this.executionStartTime : 0;
    console.error(`❌ 执行失败: ${this.currentOperation}, 耗时: ${duration}ms, 错误:`, error);

    this.isExecuting = false;
    this.executionStartTime = null;
    this.currentOperation = null;
  }

  /**
   * 更新当前操作状态
   */
  private updateOperation(operation: string) {
    this.currentOperation = operation;
    const duration = this.executionStartTime ? Date.now() - this.executionStartTime : 0;
    console.log(`🔄 ${operation} (执行中 ${Math.round(duration / 1000)}秒)`);
  }

  /**
   * 等待 IoTeX 上的 SOL 余额增加
   * @param expectedAmount 预期增加的 SOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 5 分钟
   * @param checkInterval 检查间隔（毫秒），默认 10 秒
   */
  private async waitForSolBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 600000, // 10 分钟 (跨链转账需要更长时间)
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 IoTeX SOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始余额
    let initialBalance: number;
    try {
      initialBalance = parseFloat(
        await this.mimoService.getTokenBalanceFormatted("SOL")
      );
      console.log(`📊 初始 IoTeX SOL 余额: ${initialBalance} SOL`);
    } catch (error) {
      console.error("获取初始 IoTeX SOL 余额失败:", error);
      throw new Error("无法获取 IoTeX SOL 余额，请检查网络连接");
    }

    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%
    console.log(`🎯 目标余额: ${targetBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

    let checkCount = 0;
    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const currentBalance = parseFloat(
          await this.mimoService.getTokenBalanceFormatted("SOL")
        );
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);

        // 每 3 次检查才输出一次日志，避免过多输出
        if (checkCount % 3 === 1) {
          console.log(`🔍 检查 ${checkCount} (${elapsedTime}s): 当前余额 ${currentBalance} SOL`);
        }

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialBalance;
          console.log(`✅ IoTeX SOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 IoTeX SOL 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    console.log("⏰ 达到最大等待时间，进行最终检查...");
    const finalBalance = parseFloat(
      await this.mimoService.getTokenBalanceFormatted("SOL")
    );
    const actualIncrease = finalBalance - initialBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到余额增加 ${actualIncrease.toFixed(6)} SOL，继续执行...`);
    } else {
      console.log(`❌ 等待 ${maxWaitTime / 1000} 秒后仍未检测到余额变化`);
      throw new Error(`等待 IoTeX SOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualIncrease.toFixed(6)} SOL`);
    }
  }

  /**
   * 等待 IoTeX 上的 IOTX 余额增加
   * @param expectedAmount 预期增加的 IOTX 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 3 分钟
   * @param checkInterval 检查间隔（毫秒），默认 10 秒
   */
  private async waitForIotxBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 180000, // 3 分钟
    checkInterval: number = 10000 // 10 秒
  ): Promise<void> {
    console.log(`⏳ 等待 IoTeX IOTX 余额增加 ${expectedAmount} IOTX...`);

    // 获取初始余额
    const initialBalance = parseFloat(
      await this.mimoService.getTokenBalanceFormatted("IOTX")
    );

    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const currentBalance = parseFloat(
          await this.mimoService.getTokenBalanceFormatted("IOTX")
        );

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialBalance;
          console.log(`✅ IOTX 余额已增加 ${actualIncrease.toFixed(2)} IOTX`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 IOTX 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    const finalBalance = parseFloat(
      await this.mimoService.getTokenBalanceFormatted("IOTX")
    );
    const actualIncrease = finalBalance - initialBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到余额增加 ${actualIncrease.toFixed(2)} IOTX，继续执行...`);
    } else {
      throw new Error(`等待 IOTX 到账超时，预期增加 ${expectedAmount} IOTX，实际增加 ${actualIncrease.toFixed(2)} IOTX`);
    }
  }

  /**
   * 等待 Solana 上的 WSOL 余额增加（跨链转账接收的是 WSOL）
   * @param expectedAmount 预期增加的 WSOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 10 分钟
   * @param checkInterval 检查间隔（毫秒），默认 15 秒
   */
  private async waitForSolanaWsolBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 600000, // 10 分钟 (跨链转账需要更长时间)
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 Solana WSOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始 WSOL 余额（跨链转账接收的是 WSOL，不是 native SOL）
    let initialWsolBalance: number;
    let initialNativeBalance: number;
    try {
      const balances = await this.solanaService.getTotalSolBalance();
      initialWsolBalance = balances.wsol;
      initialNativeBalance = balances.native;
      console.log(`📊 初始 Solana 余额: Native SOL: ${initialNativeBalance} SOL, WSOL: ${initialWsolBalance} SOL`);
    } catch (error) {
      console.error("获取初始 Solana 余额失败:", error);
      throw new Error("无法获取 Solana 余额，请检查网络连接");
    }

    const startTime = Date.now();
    const targetWsolBalance = initialWsolBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%
    console.log(`🎯 目标 WSOL 余额: ${targetWsolBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

    let checkCount = 0;
    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const currentBalances = await this.solanaService.getTotalSolBalance();
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);

        console.log(`🔍 第 ${checkCount} 次检查 (${elapsedTime}s): Native: ${currentBalances.native} SOL, WSOL: ${currentBalances.wsol} SOL`);

        if (currentBalances.wsol >= targetWsolBalance) {
          const actualIncrease = currentBalances.wsol - initialWsolBalance;
          console.log(`✅ Solana WSOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
          return;
        }

        const remainingTime = Math.floor((maxWaitTime - (Date.now() - startTime)) / 1000);
        console.log(`⏳ 余额未达到目标，${checkInterval / 1000} 秒后重新检查 (剩余 ${remainingTime}s)`);

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 Solana SOL 余额时出错:", error);
        console.log(`⏳ 网络错误，${checkInterval / 1000} 秒后重试...`);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    console.log("⏰ 达到最大等待时间，进行最终检查...");
    let finalBalances: { native: number; wsol: number; total: number };
    try {
      finalBalances = await this.solanaService.getTotalSolBalance();
    } catch (error) {
      console.error("最终余额检查失败:", error);
      throw new Error("无法完成最终余额检查，请手动验证转账状态");
    }

    const actualWsolIncrease = finalBalances.wsol - initialWsolBalance;
    const actualNativeIncrease = finalBalances.native - initialNativeBalance;
    console.log(`📊 最终余额: Native: ${finalBalances.native} SOL, WSOL: ${finalBalances.wsol} SOL`);
    console.log(`📊 余额变化: Native: ${actualNativeIncrease.toFixed(6)} SOL, WSOL: ${actualWsolIncrease.toFixed(6)} SOL`);

    if (actualWsolIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到 WSOL 余额增加 ${actualWsolIncrease.toFixed(6)} SOL，继续执行...`);
    } else {
      console.log(`❌ 等待 ${maxWaitTime / 1000} 秒后仍未检测到 WSOL 余额变化`);
      console.log(`💡 建议: 请手动检查 Solana 地址 ${this.config.walletAddresses.solana} 的 WSOL 余额`);
      throw new Error(`等待 Solana WSOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualWsolIncrease.toFixed(6)} SOL`);
    }
  }

  /**
   * 等待 Solana 上的 native SOL 余额增加（CEX 提现接收的是 native SOL）
   * @param expectedAmount 预期增加的 native SOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 5 分钟
   * @param checkInterval 检查间隔（毫秒），默认 15 秒
   */
  private async waitForSolanaBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 300000, // 5 分钟 (CEX 提现相对较快)
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 Solana native SOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始 native SOL 余额
    let initialNativeBalance: number;
    try {
      initialNativeBalance = await this.solanaService.getSolBalance();
      console.log(`📊 初始 Solana native SOL 余额: ${initialNativeBalance} SOL`);
    } catch (error) {
      console.error("获取初始 Solana native SOL 余额失败:", error);
      throw new Error("无法获取 Solana native SOL 余额，请检查网络连接");
    }

    const startTime = Date.now();
    const targetBalance = initialNativeBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%
    console.log(`🎯 目标 native SOL 余额: ${targetBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

    let checkCount = 0;
    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const currentBalance = await this.solanaService.getSolBalance();
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);

        // 每 3 次检查才输出一次日志，避免过多输出
        if (checkCount % 3 === 1) {
          console.log(`🔍 检查 ${checkCount} (${elapsedTime}s): 当前 native SOL 余额 ${currentBalance} SOL`);
        }

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialNativeBalance;
          console.log(`✅ Solana native SOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 Solana native SOL 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    console.log("⏰ 达到最大等待时间，进行最终检查...");
    const finalBalance = await this.solanaService.getSolBalance();
    const actualIncrease = finalBalance - initialNativeBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到 native SOL 余额增加 ${actualIncrease.toFixed(6)} SOL，继续执行...`);
    } else {
      console.log(`❌ 等待 ${maxWaitTime / 1000} 秒后仍未检测到 native SOL 余额变化`);
      throw new Error(`等待 Solana native SOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualIncrease.toFixed(6)} SOL`);
    }
  }

  /**
   * 分析套利机会
   */
  async analyzeArbitrageOpportunity() {
    console.log("🔍 分析套利机会...");

    // 获取价格数据
    const cexSolPrice = await this.cexService.getSolPrice(); // Binance SOL 价格
    const iotxPrice = await this.cexService.getIotxPrice(); // IOTX 价格

    // 获取 DEX 上 SOL 可以换多少 IOTX (通过 MIMO)
    const dexSolToIotxRate = await this.mimoService.getSolToIotxRate(1); // 1 SOL 可以换多少 IOTX

    // 计算 CEX 上 SOL 可以换多少 IOTX
    // CEX: SOL -> USDT -> IOTX
    const cexSolToIotxRate = cexSolPrice / iotxPrice; // 1 SOL 可以换多少 IOTX

    // 计算汇率差异
    const rateDifference = Math.abs(cexSolToIotxRate - dexSolToIotxRate);

    console.log(`💰 CEX: 1 SOL = ${cexSolToIotxRate.toFixed(2)} IOTX | DEX: 1 SOL = ${dexSolToIotxRate.toFixed(2)} IOTX | 差异: ${rateDifference.toFixed(2)} IOTX`);

    // 确定套利方向
    const direction = cexSolToIotxRate > dexSolToIotxRate ? "cex_to_dex" : "dex_to_cex";

    // 扣除 MIMO v3 的 0.3% 交易费用
    const mimoFeeRate = 0.003; // 0.3%
    let adjustedRateDifference: number;

    if (direction === "cex_to_dex") {
      // CEX -> DEX: 在 DEX 用 SOL 换 IOTX 时需要扣除手续费
      // 手续费按输出的 IOTX 计算
      const feeInIotx = dexSolToIotxRate * mimoFeeRate;
      adjustedRateDifference = rateDifference - feeInIotx;
      console.log(`扣除 MIMO 0.3% 手续费后的差异: ${adjustedRateDifference.toFixed(2)} IOTX`);
    } else {
      // DEX -> CEX: 在 DEX 用 IOTX 换 SOL 时需要扣除手续费
      // 手续费按输入的 IOTX 计算
      const feeInIotx = cexSolToIotxRate * mimoFeeRate;
      adjustedRateDifference = rateDifference - feeInIotx;
      console.log(`扣除 MIMO 0.3% 手续费后的差异: ${adjustedRateDifference.toFixed(2)} IOTX`);
    }

    // 判断是否有套利机会：差价超过 50 IOTX
    const profitable = adjustedRateDifference > 50;

    const opportunity = {
      cexSolPrice,
      iotxPrice,
      cexSolToIotxRate,
      dexSolToIotxRate,
      rateDifference,
      adjustedRateDifference,
      direction,
      profitable,
    };

    return opportunity;
  }

  /**
   * 执行套利策略：CEX -> DEX
   * CEX 价格高于 DEX 价格时执行
   */
  async executeCexToDexArbitrage(usdtAmount: number) {
    console.log(`🚀 执行 CEX -> DEX 套利策略，金额: $${usdtAmount}`);

    try {
      // 记录初始钱包余额
      const initialBalances = {
        solOnChain: await this.solanaService.getSolBalance(),
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted(
          "IOTX",
          this.mimoService.getWalletAddress()
        ),
      };

      // 1. 在 CEX 上买 IOTX
      this.updateOperation("在 CEX 上买 IOTX");
      const buyOrder = await this.cexService.buyIotx(usdtAmount);
      let iotxAmount = buyOrder.filled || buyOrder.amount;
      iotxAmount = Math.floor(iotxAmount);

      // 短暂等待确保订单处理完成
      await new Promise((resolve) => setTimeout(resolve, 10000));

      // 2. 将 IOTX 提现到 IoTeX 钱包
      this.updateOperation("将 IOTX 提现到 IoTeX 钱包");
      await this.cexService.withdrawIotx(
        iotxAmount,
        this.config.walletAddresses.iotex
      );
      await this.waitForIotxBalanceIncrease(iotxAmount);

      // 3. 在 MIMO 将 IOTX 换成 SOL
      this.updateOperation("在 MIMO 将 IOTX 换成 SOL");
      const swapResult = await this.mimoService.swapIotxToSol(iotxAmount);

      // 4. 将 SOL 通过跨链桥转移到 Solana
      this.updateOperation("将 SOL 通过跨链桥转移到 Solana");
      const bridgeResult =
        await this.bridgeService.transferSolFromIotexToSolana(
          swapResult.solAmount,
          this.config.walletAddresses.solana
        );
      await this.waitForSolanaWsolBalanceIncrease(bridgeResult.receivedAmount, 1000 * 60 * 10);

      const solanaService = new SolanaService({
        rpcUrl: this.bridgeService.config.solanaRpcUrl,
        privateKey: this.bridgeService.config.solanaPrivateKey,
      })

      // 5. 解包 wSOL 并转账到 CEX
      console.log("步骤 5: 解包 wSOL 并转账到 CEX");
      await solanaService.unwrapSol();

      const bnDepositSolAddress = await this.cexService.getSolDepositAddress();
      const transferTx = await solanaService.transfer(bnDepositSolAddress, swapResult.solAmount);
      console.log(`💸 SOL 转账哈希: ${transferTx}`);

      await this.cexService.waitForSolBalanceIncrease(swapResult.solAmount);

      // 6. 在 CEX 卖出 SOL
      console.log("步骤 6: 在 CEX 卖出 SOL");
      await this.cexService.sellSymbol("SOLUSDT", swapResult.solAmount);
      
      // 记录最终钱包余额
      const finalBalances = {
        solOnChain: await this.solanaService.getSolBalance(),
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted(
          "IOTX",
          this.mimoService.getWalletAddress()
        ),
      };

      console.log("📊 最终钱包余额:", {
        "SOL (链上)": `${finalBalances.solOnChain} SOL`,
        "IOTX (链上)": `${finalBalances.iotxOnChain} IOTX`,
      });

      // 计算钱包余额变化
      const balanceChanges = {
        solOnChain: finalBalances.solOnChain - initialBalances.solOnChain,
        iotxOnChain:
          parseFloat(finalBalances.iotxOnChain) -
          parseFloat(initialBalances.iotxOnChain),
      };

      // 余额变化已计算，用于后续盈利分析

      // 计算盈利（注意：CEX到DEX策略目前未完成最后的卖出步骤）
      const totalCost =
        buyOrder.cost || buyOrder.filled * buyOrder.average! || usdtAmount; // 实际成交金额
      const currentSolPrice = await this.cexService.getSolPrice();
      const currentIotxPrice = await this.cexService.getIotxPrice();
      const estimatedSolValue = bridgeResult.receivedAmount * currentSolPrice; // 估算最终SOL价值
      const tradingNetProfit = estimatedSolValue - totalCost;
      const tradingProfitPercentage = (tradingNetProfit / totalCost) * 100;

      // 基于钱包余额变化计算实际净盈利
      const actualNetProfit =
        balanceChanges.solOnChain * currentSolPrice +
        balanceChanges.iotxOnChain * currentIotxPrice;
      const actualProfitPercentage =
        totalCost > 0 ? (actualNetProfit / totalCost) * 100 : 0;

      const result = {
        strategy: "cex_to_dex",
        initialAmount: usdtAmount,
        buyOrder,
        swapResult,
        bridgeResult,
        // 盈利分析（估算值，因为未完成最终卖出）
        profitAnalysis: {
          // 基于交易记录的分析
          tradingCost: totalCost,
          tradingRevenue: estimatedSolValue,
          tradingNetProfit,
          tradingProfitPercentage,
          // 基于钱包余额变化的分析（更准确）
          initialBalances,
          finalBalances,
          balanceChanges,
          actualNetProfit,
          actualProfitPercentage,
          fees: {
            buyOrderFee: buyOrder.fee?.cost || 0,
            bridgeFee: bridgeResult.bridgeFee || 0,
            swapFee: parseFloat(swapResult.priceImpact) || 0,
          },
        },
        note: "盈利为估算值，需要手动完成SOL卖出步骤",
        status: "completed",
      };

      console.log(`💰 套利完成 - 投入: $${totalCost} | 估算盈利: $${tradingNetProfit.toFixed(2)} (${tradingProfitPercentage.toFixed(2)}%)`);
      return result;
    } catch (error) {
      console.error("❌ CEX -> DEX 套利失败:", error);
      throw error;
    }
  }

  /**
   * 执行套利策略：DEX -> CEX
   * DEX 价格高于 CEX 价格时执行
   */
  async executeDexToCexArbitrage(usdtAmount: number) {
    console.log(`🚀 执行 DEX -> CEX 套利策略，金额: $${usdtAmount}`);

    try {
      // 记录初始链上钱包余额
      const initialBalances = {
        solOnChain: await this.solanaService.getSolBalance(), // Solana 链上 SOL 余额
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted("IOTX"), // IoTeX 链上 IOTX 余额
      };
      // 预检查：计算能买到的 SOL 数量是否满足最小提现要求
      const minWithdrawAmount = 0.1;
      const solPrice = await this.cexService.getSolPrice();
      const estimatedSolAmount = usdtAmount / solPrice;

      if (estimatedSolAmount < minWithdrawAmount) {
        throw new Error(
          `预估 SOL 购买数量 ${estimatedSolAmount.toFixed(
            4
          )} 小于币安最小提现要求 ${minWithdrawAmount} SOL，需要至少 $${(
            minWithdrawAmount * solPrice
          ).toFixed(2)} USDT`
        );
      }

      // 1. 在 CEX 上买 SOL
      console.log("步骤 1: 在 CEX 上买 SOL");
      const buyOrder = await this.cexService.buySol(usdtAmount);
      const solAmount = buyOrder.filled || buyOrder.amount;

      // 二次检查实际买入金额
      if (solAmount < minWithdrawAmount) {
        throw new Error(
          `实际 SOL 购买数量 ${solAmount} 小于币安最小提现要求 ${minWithdrawAmount} SOL`
        );
      }

      // 短暂等待确保订单处理完成
      await new Promise((resolve) => setTimeout(resolve, 5_000));

      // 2. 将 SOL 提现到 Solana 钱包
      console.log("步骤 2: 将 SOL 提现到 Solana 钱包");
      await this.cexService.withdrawSol(
        solAmount,
        this.config.walletAddresses.solana
      );
      await this.waitForSolanaBalanceIncrease(solAmount);

      // 3. 通过跨链桥将 SOL 转移到 IoTeX
      console.log("步骤 3: 通过跨链桥将 SOL 转移到 IoTeX");
      const bridgeResult =
        await this.bridgeService.transferSolFromSolanaToIotex(
          solAmount,
          this.config.walletAddresses.iotex
        );
      await this.waitForSolBalanceIncrease(bridgeResult.receivedAmount);

      // 4. 在 MIMO 上将 SOL 转换为 IOTX
      console.log("步骤 4: 在 MIMO 上将 SOL 转换为 IOTX");
      const swapResult = await this.mimoService.swapSolToIotx(
        bridgeResult.receivedAmount
      );

      // 5. 将 IOTX 充值到 CEX 并卖出获得 USDT
      console.log("步骤 5: 将 IOTX 充值到 CEX");
      const depositAddress = await this.cexService.getIotxDepositAddress();
      const transferHash = await this.mimoService.sendIotx(
        swapResult.iotxAmount,
        from(depositAddress).stringEth()
      );
      console.log(`💸 IOTX 转账哈希: ${transferHash}`);

      await this.cexService.waitForIotxBalanceIncrease(swapResult.iotxAmount);

      // 6. 在 CEX 卖出 IOTX
      console.log("步骤 6: 在 CEX 卖出 IOTX");
      const sellOrder = await this.cexService.sellIotx(swapResult.iotxAmount);

      // 记录最终链上钱包余额
      const finalBalances = {
        solOnChain: await this.solanaService.getSolBalance(), // Solana 链上 SOL 余额
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted("IOTX"), // IoTeX 链上 IOTX 余额
      };
      // 最终余额已记录，用于后续分析

      // 计算钱包余额变化
      const balanceChanges = {
        solOnChain: finalBalances.solOnChain - initialBalances.solOnChain,
        iotxOnChain:
          parseFloat(finalBalances.iotxOnChain) -
          parseFloat(initialBalances.iotxOnChain),
      };

      // 计算盈利（基于交易记录）
      const totalCost =
        buyOrder.cost || buyOrder.filled * buyOrder.average! || usdtAmount; // 实际成交金额
      const totalRevenue = sellOrder.filled
        ? sellOrder.filled * sellOrder.average!
        : sellOrder.cost || 0; // 卖出获得的USDT
      const tradingNetProfit = totalRevenue - totalCost;
      const tradingProfitPercentage = (tradingNetProfit / totalCost) * 100;

      // 计算实际钱包净变化（更准确的盈利计算）
      const actualNetProfit =
        balanceChanges.solOnChain * solPrice +
        balanceChanges.iotxOnChain * (await this.cexService.getIotxPrice());
      const actualProfitPercentage = (actualNetProfit / usdtAmount) * 100;

      const result = {
        strategy: "dex_to_cex",
        initialAmount: usdtAmount,
        buyOrder,
        bridgeResult,
        swapResult,
        sellOrder,
        // 盈利分析
        profitAnalysis: {
          // 基于交易记录的分析
          tradingCost: totalCost,
          tradingRevenue: totalRevenue,
          tradingNetProfit,
          tradingProfitPercentage,
          // 基于钱包余额变化的分析（更准确）
          initialBalances,
          finalBalances,
          balanceChanges,
          actualNetProfit,
          actualProfitPercentage,
          fees: {
            buyOrderFee: buyOrder.fee?.cost || 0,
            sellOrderFee: sellOrder.fee?.cost || 0,
            bridgeFee: bridgeResult.bridgeFee || 0,
            swapFee: parseFloat(swapResult.priceImpact) || 0,
          },
        },
        status: "completed",
      };

      console.log(`💰 套利完成 - 投入: $${totalCost} | 收入: $${totalRevenue.toFixed(2)} | 盈利: $${tradingNetProfit.toFixed(2)} (${tradingProfitPercentage.toFixed(2)}%)`);
      return result;
    } catch (error) {
      console.error("❌ DEX -> CEX 套利失败:", error);
      throw error;
    }
  }

  /**
   * 自动执行套利
   */
  async executeArbitrage(usdtAmount: number) {
    // 检查是否已经在执行套利
    if (this.isExecuting) {
      console.log(`⚠️ 套利正在执行中: ${this.currentOperation}, 跳过本次执行`);
      return null;
    }

    try {
      this.startExecution(`套利交易 $${usdtAmount}`);

      const opportunity = await this.analyzeArbitrageOpportunity();

      if (!opportunity.profitable) {
        console.log("❌ 当前没有盈利的套利机会");
        this.endExecution();
        return null;
      }

      console.log(`✅ 发现套利机会，方向: ${opportunity.direction}`);

      let result;
      if (opportunity.direction === "cex_to_dex") {
        result = await this.executeCexToDexArbitrage(usdtAmount);
      } else {
        result = await this.executeDexToCexArbitrage(usdtAmount);
      }

      this.endExecution();
      return result;

    } catch (error) {
      this.failExecution(error);
      throw error;
    }
  }

  /**
   * 监控套利机会
   */
  async monitorArbitrageOpportunities(intervalMs: number = 30000) {
    console.log(`🔄 开始监控套利机会，检查间隔: ${intervalMs}ms`);

    setInterval(async () => {
      try {
        // 检查是否正在执行套利
        if (this.isExecuting) {
          const status = this.getExecutionStatus();
          console.log(`⏳ 套利执行中: ${status.currentOperation}, 已执行 ${Math.round(status.executionDuration / 1000)}秒, 跳过本次监控`);
          return;
        }

        const opportunity = await this.analyzeArbitrageOpportunity();

        if (opportunity.profitable) {
          console.log(
            `🎯 发现套利机会! 方向: ${opportunity.direction}, 差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX`
          );

          // 可以在这里添加自动执行逻辑
          // if (process.env.AUTO_EXECUTE === 'true') {
          //   console.log('🤖 自动执行套利...');
          //   await this.executeArbitrage(100); // 执行 $100 的套利
          // }
        } else {
          console.log(`📊 监控中... 差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX (需要 > 50 IOTX)`);
        }
      } catch (error) {
        console.error("监控套利机会时出错:", error);
      }
    }, intervalMs);
  }
}
