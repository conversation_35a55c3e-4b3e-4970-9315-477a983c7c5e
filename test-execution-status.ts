#!/usr/bin/env ts-node

import { ArbitrageService } from "./arbitrage";
import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube-bridge";
import { SolanaService } from "./solana";

/**
 * 测试套利执行状态管理功能
 */

// 配置信息
const config = {
  binance: {
    apiKey: process.env.BINANCE_API_KEY || "",
    secret: process.env.BINANCE_API_SECRET || "",
    sandbox: false,
  },
  mimo: {
    dbUrl: process.env.MIMO_DB_URL || "postgresql://mimo:mimo@localhost:5432/mimo",
    rpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    privateKey: process.env.IOTEX_PRIVATE_KEY || "",
  },
  bridge: {
    iotexRpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    solanaRpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    iotexPrivateKey: process.env.IOTEX_PRIVATE_KEY || "",
    solanaPrivateKey: process.env.SOLANA_PRIVATE_KEY || "",
  },
  solana: {
    rpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    privateKey: process.env.SOLANA_PRIVATE_KEY || "",
  },
  arbitrage: {
    minIotxDifference: 50,
    maxTradeAmount: 100,
    walletAddresses: {
      iotex: process.env.IOTEX_WALLET_ADDRESS || "",
      solana: process.env.SOLANA_WALLET_ADDRESS || "",
    },
  },
};

async function testExecutionStatus() {
  console.log("🧪 测试套利执行状态管理...");

  try {
    // 初始化服务
    const cexService = new CexService(config.binance);
    const mimoService = new MimoService(config.mimo.dbUrl, {
      rpcUrl: config.mimo.rpcUrl,
      privateKey: config.mimo.privateKey,
    });
    const bridgeService = new IoTubeBridgeService(config.bridge);
    const solanaService = new SolanaService(config.solana);

    const arbitrageService = new ArbitrageService(
      cexService,
      mimoService,
      bridgeService,
      solanaService,
      config.arbitrage
    );

    console.log("✅ 服务初始化完成");

    // 测试 1: 检查初始状态
    console.log("\n📊 测试 1: 检查初始状态");
    console.log("是否正在执行:", arbitrageService.isArbitrageExecuting());
    console.log("执行状态:", arbitrageService.getExecutionStatus());

    // 测试 2: 模拟监控过程中的状态检查
    console.log("\n📊 测试 2: 启动监控模式");
    
    // 启动监控（短间隔用于测试）
    arbitrageService.monitorArbitrageOpportunities(5000); // 5秒间隔

    // 等待几次监控循环
    console.log("等待监控运行...");
    await new Promise(resolve => setTimeout(resolve, 20000)); // 等待20秒

    // 测试 3: 模拟执行状态
    console.log("\n📊 测试 3: 模拟套利执行");
    
    // 模拟一个长时间运行的套利操作
    const mockExecuteArbitrage = async () => {
      console.log("🚀 开始模拟套利执行...");
      
      // 模拟执行过程
      for (let i = 1; i <= 5; i++) {
        console.log(`模拟步骤 ${i}/5...`);
        await new Promise(resolve => setTimeout(resolve, 3000)); // 每步等待3秒
        
        // 检查状态
        const status = arbitrageService.getExecutionStatus();
        console.log(`当前状态: ${status.currentOperation}, 执行时间: ${Math.round(status.executionDuration / 1000)}秒`);
      }
      
      console.log("✅ 模拟套利执行完成");
    };

    // 在后台运行模拟执行
    mockExecuteArbitrage();

    // 继续监控，观察状态变化
    console.log("继续监控，观察执行状态...");
    await new Promise(resolve => setTimeout(resolve, 30000)); // 等待30秒

    console.log("\n✅ 测试完成");

  } catch (error) {
    console.error("❌ 测试失败:", error);
  }
}

/**
 * 测试并发执行保护
 */
async function testConcurrentExecutionProtection() {
  console.log("\n🧪 测试并发执行保护...");

  try {
    // 初始化服务（简化版）
    const cexService = new CexService(config.binance);
    const mimoService = new MimoService(config.mimo.dbUrl, {
      rpcUrl: config.mimo.rpcUrl,
      privateKey: config.mimo.privateKey,
    });
    const bridgeService = new IoTubeBridgeService(config.bridge);
    const solanaService = new SolanaService(config.solana);

    const arbitrageService = new ArbitrageService(
      cexService,
      mimoService,
      bridgeService,
      solanaService,
      config.arbitrage
    );

    // 模拟同时触发多个套利执行
    console.log("🚀 同时触发 3 个套利执行请求...");

    const promises = [
      arbitrageService.executeArbitrage(50),
      arbitrageService.executeArbitrage(100),
      arbitrageService.executeArbitrage(75),
    ];

    const results = await Promise.allSettled(promises);

    console.log("\n📊 执行结果:");
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`请求 ${index + 1}: 成功 -`, result.value ? "执行了套利" : "跳过执行");
      } else {
        console.log(`请求 ${index + 1}: 失败 -`, result.reason);
      }
    });

    console.log("\n✅ 并发保护测试完成");

  } catch (error) {
    console.error("❌ 并发保护测试失败:", error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log("🚀 启动套利执行状态测试...");

  const args = process.argv.slice(2);

  if (args.includes('--concurrent') || args.includes('-c')) {
    await testConcurrentExecutionProtection();
  } else {
    await testExecutionStatus();
  }

  process.exit(0);
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在退出...');
  process.exit(0);
});

// 启动测试
if (require.main === module) {
  main();
}

export { testExecutionStatus, testConcurrentExecutionProtection };
