import { CexService } from "../cex";
import { MimoService } from "../mimo";
import { SolanaService } from "../solana";

/**
 * 余额检测功能测试
 * 测试新的智能等待机制是否正常工作
 */
class BalanceDetectionTester {
  private mimoService: MimoService;
  private solanaService: SolanaService;

  constructor() {
    // 使用测试配置
    const config = {
      binance: {
        apiKey: process.env.BINANCE_API_KEY || "",
        secret: process.env.BINANCE_API_SECRET || "",
        sandbox: true, // 使用沙盒环境
      },
      mimo: {
        dbUrl: process.env.MIMO_DB_URL || "postgresql://mimo:<EMAIL>/mimo",
        rpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
        routerV3: "0x17C1Ae82D99379240059940093762c5e4539aba5",
        privateKey: process.env.IOTEX_PRIVATE_KEY || 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a',
        tokens: {
          SOL: {
            address: "0xa1f3f211d9b33f2086a800842836d67f139b9a7a",
            symbol: "SOL",
            decimals: 9,
          },
          IOTX: {
            address: "IOTX",
            symbol: "IOTX",
            decimals: 18,
          },
          WIOTX: {
            address: "0xA00744882684C3e4747faEFD68D283eA44099D03",
            symbol: "WIOTX",
            decimals: 18,
          },
        },
      },
      bridge: {
        iotexRpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
        solanaRpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
        bridgeContractAddress: "******************************************",
        iotexPrivateKey: process.env.IOTEX_PRIVATE_KEY,
        solanaPrivateKey: process.env.SOLANA_PRIVATE_KEY,
      },
      solana: {
        rpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
        privateKey: process.env.SOLANA_PRIVATE_KEY || "",
      },
      arbitrage: {
        minPriceDifference: 2,
        maxTradeAmount: 10, // 小额测试
        slippage: 1,
        walletAddresses: {
          iotex: process.env.IOTEX_WALLET_ADDRESS || "******************************************",
          solana: process.env.SOLANA_WALLET_ADDRESS || "57hfxmbXd2SC5YESxT2bL8mSG2adoV8UJpSWSSNCxJjK",
        },
      },
    };

    // 初始化服务
    this.mimoService = new MimoService(config.mimo.dbUrl, {
      rpcUrl: config.mimo.rpcUrl,
      privateKey: config.mimo.privateKey,
      tokens: config.mimo.tokens,
    });
    this.solanaService = new SolanaService(config.solana);
  }

  /**
   * 测试 IoTeX 上的 SOL 余额检测
   */
  async testSolBalanceDetection() {
    console.log("🧪 测试 IoTeX 上的 SOL 余额检测...");
    
    try {
      // 获取当前余额
      const currentBalance = await this.mimoService.getTokenBalanceFormatted("SOL");
      console.log(`📊 当前 IoTeX SOL 余额: ${currentBalance} SOL`);
      
      // 测试余额检测逻辑（模拟等待很小的增量）
      console.log("🔍 测试余额检测逻辑（10秒超时）...");
      
      try {
        // 使用私有方法进行测试（注意：这在实际代码中需要将方法改为 public 或创建测试专用方法）
        // await this.arbitrageService.waitForSolBalanceIncrease(0.001, 10000, 2000);
        console.log("⚠️ 无法直接测试私有方法，需要实际的跨链转账来触发余额变化");
      } catch (error) {
        console.log("✅ 超时机制正常工作:", (error as Error).message);
      }
      
      return true;
    } catch (error) {
      console.error("❌ SOL 余额检测测试失败:", (error as Error).message);
      return false;
    }
  }

  /**
   * 测试 IoTeX 上的 IOTX 余额检测
   */
  async testIotxBalanceDetection() {
    console.log("🧪 测试 IoTeX 上的 IOTX 余额检测...");
    
    try {
      // 获取当前余额
      const currentBalance = await this.mimoService.getTokenBalanceFormatted("IOTX");
      console.log(`📊 当前 IoTeX IOTX 余额: ${currentBalance} IOTX`);
      
      console.log("✅ IOTX 余额读取正常");
      return true;
    } catch (error) {
      console.error("❌ IOTX 余额检测测试失败:", (error as Error).message);
      return false;
    }
  }

  /**
   * 测试 Solana 上的 SOL 余额检测
   */
  async testSolanaBalanceDetection() {
    console.log("🧪 测试 Solana 上的 SOL 余额检测...");

    try {
      // 获取当前余额
      const currentBalance = await this.solanaService.getSolBalance();
      console.log(`📊 当前 Solana SOL 余额: ${currentBalance} SOL`);

      console.log("✅ Solana SOL 余额读取正常");
      return true;
    } catch (error) {
      console.error("❌ Solana SOL 余额检测测试失败:", (error as Error).message);
      return false;
    }
  }

  /**
   * 测试 CEX 上的 IOTX 余额检测
   */
  async testCexIotxBalanceDetection() {
    console.log("🧪 测试 CEX 上的 IOTX 余额检测...");

    try {
      // 获取当前余额 - 使用已初始化的 cexService
      const cexService = new CexService({
        apiKey: process.env.BINANCE_API_KEY || "",
        secret: process.env.BINANCE_API_SECRET || "",
      });

      const currentBalance = await cexService.getIotxBalance();
      console.log(`📊 当前 CEX IOTX 余额: ${currentBalance} IOTX`);

      console.log("✅ CEX IOTX 余额读取正常");
      return true;
    } catch (error) {
      console.error("❌ CEX IOTX 余额检测测试失败:", (error as Error).message);
      return false;
    }
  }

  /**
   * 测试 CEX 上的 SOL 余额检测
   */
  async testCexSolBalanceDetection() {
    console.log("🧪 测试 CEX 上的 SOL 余额检测...");

    try {
      // 获取当前余额 - 使用已初始化的 cexService
      const cexService = new CexService({
        apiKey: process.env.BINANCE_API_KEY || "",
        secret: process.env.BINANCE_API_SECRET || "",
      });

      const currentBalance = await cexService.getSolBalance();
      console.log(`📊 当前 CEX SOL 余额: ${currentBalance} SOL`);

      console.log("✅ CEX SOL 余额读取正常");
      return true;
    } catch (error) {
      console.error("❌ CEX SOL 余额检测测试失败:", (error as Error).message);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log("🚀 开始余额检测功能测试...\n");

    const results = {
      solBalance: await this.testSolBalanceDetection(),
      iotxBalance: await this.testIotxBalanceDetection(),
      solanaBalance: await this.testSolanaBalanceDetection(),
      cexIotxBalance: await this.testCexIotxBalanceDetection(),
      cexSolBalance: await this.testCexSolBalanceDetection(),
    };

    console.log("\n📊 测试结果汇总:");
    console.log(`IoTeX SOL 余额检测: ${results.solBalance ? "✅ 通过" : "❌ 失败"}`);
    console.log(`IoTeX IOTX 余额检测: ${results.iotxBalance ? "✅ 通过" : "❌ 失败"}`);
    console.log(`Solana SOL 余额检测: ${results.solanaBalance ? "✅ 通过" : "❌ 失败"}`);
    console.log(`CEX IOTX 余额检测: ${results.cexIotxBalance ? "✅ 通过" : "❌ 失败"}`);
    console.log(`CEX SOL 余额检测: ${results.cexSolBalance ? "✅ 通过" : "❌ 失败"}`);

    const allPassed = Object.values(results).every(result => result);
    console.log(`\n🎯 总体结果: ${allPassed ? "✅ 所有测试通过" : "❌ 部分测试失败"}`);

    return allPassed;
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new BalanceDetectionTester();
  tester.runAllTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error("测试执行失败:", error);
      process.exit(1);
    });
}

export { BalanceDetectionTester };
