# 套利系统单元测试

本目录包含了套利系统各个组件的单元测试，用于验证系统功能的正确性和稳定性。

## 测试结构

```
test/
├── README.md           # 测试说明文档
├── run-tests.ts        # 测试运行器
├── cex.test.ts         # CEX 服务测试
├── mimo.test.ts        # MIMO DEX 服务测试
└── bridge.test.ts      # 桥接服务测试
```

## 快速开始

### 运行所有测试
```bash
bun run test:unit
```

### 运行单个测试模块
```bash
# CEX 服务测试
bun run test:cex

# MIMO DEX 服务测试
bun run test:mimo

# 桥接服务测试
bun run test:bridge
```

## 测试模块说明

### 1. CEX 服务测试 (`cex.test.ts`)

测试 Binance CEX 服务的功能：
- ✅ **价格获取**: 测试 SOL 和 IOTX 价格获取
- ✅ **充值地址获取**: 测试获取充值地址功能
- ✅ **买入功能**: 模拟测试买入功能（不实际执行）
- ✅ **提现功能**: 模拟测试提现功能（不实际执行）

### 2. MIMO DEX 服务测试 (`mimo.test.ts`)

测试 MIMO DEX 服务的功能：
- ✅ **价格获取**: 测试 SOL 和 IOTX 价格获取
- ✅ **代币信息**: 测试代币信息查询
- ✅ **交换模拟**: 模拟测试交换功能（不实际执行）
- ✅ **钱包连接**: 测试钱包连接状态检查
- ✅ **服务初始化**: 测试服务初始化状态

### 3. 桥接服务测试 (`bridge.test.ts`)

测试 IoTube 桥接服务的功能：
- ✅ **支持代币列表**: 测试获取支持的代币列表
- ✅ **费用计算**: 测试桥接费用计算
- ✅ **地址验证**: 测试 Solana 和 IoTeX 地址验证
- ✅ **转账限制**: 测试转账金额限制查询
- ✅ **服务连接**: 测试桥接服务连接状态

## 环境配置

### 必需的环境变量

在运行测试前，请确保 `.env` 文件包含以下配置：

```env
# Binance API 配置（CEX 测试）
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret

# 区块链 RPC 配置
IOTEX_RPC_URL=https://babel-api.mainnet.iotex.io
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# 私钥配置（可选，用于高级测试）
IOTEX_PRIVATE_KEY=your_iotex_private_key
SOLANA_PRIVATE_KEY=your_solana_private_key
```

### 测试模式

- **安全模式**: 大部分测试都在安全模式下运行，不会执行实际的交易或转账
- **只读测试**: 价格查询、代币信息等只读操作会连接真实的 API
- **模拟测试**: 交易、转账等操作只进行模拟，不会产生实际费用

## 测试结果解读

### 成功示例
```
📊 测试总结
============================================================
总测试数: 14
✅ 通过: 14
❌ 失败: 0
📈 成功率: 100.0%

🎉 所有测试通过！套利系统各组件运行正常。
```

### 部分失败示例
```
📊 测试总结
============================================================
总测试数: 14
✅ 通过: 12
❌ 失败: 2
📈 成功率: 85.7%

⚠️  部分测试失败，请检查相关服务配置。
```

## 常见问题

### 1. API 密钥相关错误

**问题**: CEX 测试失败，提示 API 密钥无效

**解决方案**:
- 检查 `.env` 文件中的 `BINANCE_API_KEY` 和 `BINANCE_API_SECRET`
- 确保 API 密钥有效且具有必要的权限
- 某些测试在没有有效 API 密钥时会自动跳过

### 2. 网络连接问题

**问题**: 价格获取或区块链连接失败

**解决方案**:
- 检查网络连接
- 确认 RPC 端点可访问
- 某些地区可能需要使用代理

### 3. 依赖问题

**问题**: 模块导入失败

**解决方案**:
```bash
# 重新安装依赖
bun install

# 检查 TypeScript 配置
bun run type-check
```

## 开发指南

### 添加新测试

1. 在相应的测试文件中添加新的测试方法
2. 在 `runAllTests()` 方法中注册新测试
3. 确保测试方法返回布尔值（成功/失败）

### 测试最佳实践

- ✅ 使用描述性的测试名称
- ✅ 提供详细的错误信息
- ✅ 避免执行实际的交易操作
- ✅ 使用环境变量进行配置
- ✅ 处理网络异常和 API 限制

## 性能基准

典型的测试运行时间：
- CEX 服务测试: ~5-10 秒
- MIMO DEX 服务测试: ~3-8 秒
- 桥接服务测试: ~2-5 秒
- 总计: ~10-25 秒

## 技术栈

- **运行时**: Bun (推荐) 或 Node.js
- **语言**: TypeScript
- **测试框架**: 自定义轻量级测试框架
- **API 集成**: Binance API, IoTeX RPC, Solana RPC

---

💡 **提示**: 定期运行测试以确保系统稳定性，特别是在修改核心代码后。