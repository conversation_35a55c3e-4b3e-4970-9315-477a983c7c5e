/**
 * 桥接服务单元测试
 * 测试跨链桥接功能
 */

import { IoTubeBridgeService } from '../iotube-bridge';

class BridgeTester {
  private bridgeService: IoTubeBridgeService;

  constructor() {
    this.bridgeService = new IoTubeBridgeService({
      iotexRpcUrl: process.env.IOTEX_RPC_URL || 'https://babel-api.mainnet.iotex.io',
      solanaRpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
      bridgeContractAddress: '0x0000000000000000000000000000000000000000',
      iotexPrivateKey: 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a',
      // 使用有效的base58格式的Solana私钥（测试用）
      solanaPrivateKey: '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf'
    });
  }

  /**
   * 测试支持的代币列表
   */
  async testGetSupportedTokens() {
    console.log('🧪 测试桥接支持的代币...');
    try {
      const tokens = await this.bridgeService.getSupportedTokens();
      console.log('✅ 支持的代币数量:', tokens.length);
      
      if (tokens.length > 0) {
        console.log('✅ 代币列表:', tokens.map((t: any) => `${t.symbol} (${t.name})`));
        return true;
      } else {
        console.log('❌ 代币列表为空');
        return false;
      }
    } catch (error) {
      console.error('❌ 获取支持代币失败:', (error as Error).message);
      return false;
    }
  }

  /**
   * 测试桥接费用计算
   */
  async testCalculateFee() {
    console.log('🧪 测试桥接费用计算...');
    try {
      // await this.bridgeService.transferSolFromIotexToSolana(0.01, '57hfxmbXd2SC5YESxT2bL8mSG2adoV8UJpSWSSNCxJjK', 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a');
      const result = await this.bridgeService.transferSolFromSolanaToIotex(0.1, '0xD12477a364886B979c2Cc25f79A51d35aD0bF53D');
      
      if (result && result.status === 'completed') {
        console.log('✅ 跨链转账成功完成');
        return true;
      } else {
        console.log('❌ 跨链转账未完成或状态异常');
        return false;
      }
    } catch (error) {
      console.error('❌ 费用计算失败:', (error as Error).message);
      return false;
    }
  }

  /**
   * 测试地址验证功能
   */
  async testAddressValidation() {
    console.log('🧪 测试地址验证功能...');
    try {
      const solanaAddress = 'So11111111111111111111111111111111111111112';
      const iotexAddress = '0xa1f3f211d9b33f2086a800842836d67f139b9a7a';
      
      const solanaValid = this.bridgeService.validateAddress(solanaAddress, 'Solana');
      const iotexValid = this.bridgeService.validateAddress(iotexAddress, 'IoTeX');
      
      console.log('✅ 地址验证测试:', {
        solanaAddress: solanaAddress.substring(0, 20) + '...',
        solanaValid,
        iotexAddress: iotexAddress.substring(0, 20) + '...',
        iotexValid
      });
      
      if (solanaValid && iotexValid) {
        console.log('✅ 地址验证有效');
        return true;
      } else {
        console.log('❌ 地址验证无效');
        return false;
      }
    } catch (error) {
      console.error('❌ 地址验证失败:', (error as Error).message);
      return false;
    }
  }

  /**
   * 测试转账金额限制
   */
  async testTokenLookup() {
    console.log('🧪 测试转账金额限制...');
    try {
      const minAmountSOL = await this.bridgeService.getMinTransferAmount('SOL');
      const maxAmountSOL = await this.bridgeService.getMaxTransferAmount('SOL');
      const minAmountIOTX = await this.bridgeService.getMinTransferAmount('IOTX');
      const maxAmountIOTX = await this.bridgeService.getMaxTransferAmount('IOTX');
      
      console.log('✅ 转账限制查询成功:', {
        SOL: {
          minAmount: minAmountSOL,
          maxAmount: maxAmountSOL
        },
        IOTX: {
          minAmount: minAmountIOTX,
          maxAmount: maxAmountIOTX
        }
      });
      
      // 测试金额验证
      const validAmount = 10;
      const solValidation = await this.bridgeService.validateTransferAmount('SOL', validAmount);
      const iotxValidation = await this.bridgeService.validateTransferAmount('IOTX', validAmount);
      
      console.log('✅ 金额验证测试:', {
        testAmount: validAmount,
        solValid: solValidation.valid,
        iotxValid: iotxValidation.valid
      });
      
      if (minAmountSOL >= 0 && maxAmountSOL > minAmountSOL && minAmountIOTX >= 0 && maxAmountIOTX > minAmountIOTX) {
        console.log('✅ 转账限制有效');
        return true;
      } else {
        console.log('❌ 转账限制无效');
        return false;
      }
    } catch (error) {
      console.error('❌ 转账限制查询失败:', (error as Error).message);
      return false;
    }
  }

  /**
   * 测试服务连接状态
   */
  async testBridgeLimits() {
    console.log('🧪 测试服务连接状态...');
    try {
      // 测试获取支持的代币（验证服务是否正常）
      const tokens = await this.bridgeService.getSupportedTokens();
      
      // 测试费用计算（验证计算功能）
      const feeInfo = await this.bridgeService.getBridgeFee('Solana', 'IoTeX', 10);
      
      console.log('✅ 服务连接测试成功:', {
        tokensAvailable: tokens.length > 0,
        feeCalculationWorking: feeInfo.totalFee >= 0,
        serviceStatus: '正常'
      });
      
      if (tokens.length > 0 && feeInfo.totalFee >= 0) {
        console.log('✅ 桥接服务连接有效');
        return true;
      } else {
        console.log('❌ 桥接服务连接无效');
        return false;
      }
    } catch (error) {
      console.error('❌ 桥接服务连接失败:', (error as Error).message);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始桥接服务测试...');
    const tests = [
      // { name: '支持代币列表', test: () => this.testGetSupportedTokens() },
      { name: '费用计算', test: () => this.testCalculateFee() },
      // { name: '地址验证', test: () => this.testAddressValidation() },
      // { name: '代币查找', test: () => this.testTokenLookup() },
      // { name: '桥接限制', test: () => this.testBridgeLimits() }
    ]

    let passed = 0;
    let total = tests.length;

    for (const { name, test } of tests) {
      console.log(`\n📋 测试: ${name}`);
      const result = await test();
      if (result) {
        passed++;
        console.log(`✅ ${name} - 通过`);
      } else {
        console.log(`❌ ${name} - 失败`);
      }
    }

    console.log('\n' + '=' .repeat(50));
    console.log(`📊 桥接测试结果: ${passed}/${total} 通过`);
    
    if (passed === total) {
      console.log('🎉 所有桥接测试通过！');
    } else {
      console.log('⚠️  部分桥接测试失败，请检查配置');
    }

    return { passed, total };
  }
}

// 如果直接运行此文件
if (import.meta.main) {
  const tester = new BridgeTester();
  tester.runAllTests().catch(console.error);
}

// 导出测试函数供测试运行器使用
export async function runAllTests() {
  const tester = new BridgeTester();
  return await tester.runAllTests();
}

export { BridgeTester };