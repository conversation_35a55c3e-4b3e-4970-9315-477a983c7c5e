import ccxt, { Exchange } from "ccxt";

/**
 * CEX 交易所操作模块
 * 负责与中心化交易所（币安）的交互
 */
export class CexService {
  private binance: Exchange;

  constructor(config: { apiKey: string; secret: string }) {
    this.binance = new ccxt.binance(config);
  }

  /**
   * 获取 CEX 上 SOL 的价格
   */
  async getSolPrice(): Promise<number> {
    const response = await fetch(
      "https://api.binance.com/api/v3/ticker/price?symbol=SOLUSDT"
    );
    if (!response.ok) {
      throw new Error(`Binance API error: ${response.status}`);
    }
    const data = await response.json();
    // @ts-ignore
    return parseFloat(data.price);
  }

  /**
   * 获取 CEX 上 IOTX 的价格
   */
  async getIotxPrice(): Promise<number> {
    const response = await fetch(
      "https://api.binance.com/api/v3/ticker/price?symbol=IOTXUSDT"
    );
    if (!response.ok) {
      throw new Error(`Binance API error: ${response.status}`);
    }
    const data = await response.json();
    // @ts-ignore
    return parseFloat(data.price);
  }

  /**
   * 在 CEX 上买 IOTX
   */
  async buyIotx(usdtAmount: number) {
    const order = await this.binance.createMarketBuyOrder(
      "IOTXUSDT",
      0, // 占位符，实际使用 quoteOrderQty
      { quoteOrderQty: usdtAmount }
    );
    return order;
  }

  /**
   * 在 CEX 上买 SOL
   */
  async buySol(usdtAmount: number) {
    const order = await this.binance.createMarketBuyOrder(
      "SOLUSDT",
      0, // 占位符，实际使用 quoteOrderQty
      { quoteOrderQty: usdtAmount }
    );
    return order;
  }


  /**
   * 从 CEX 提现 IOTX 到指定地址
   */
  async withdrawIotx(amount: number, address: string) {
    // 发起提现请求
    const withdrawal = await this.binance.withdraw(
      "IOTX",
      amount,
      address,
      undefined,
      { network: "IOTX" }
    );
    // console.log("🚀 ~ CexService ~ withdrawIotx ~ withdrawal:", withdrawal);

    return {
      ...withdrawal
    };
  }

  /**
   * 查询最近的充值订单
   */
  async getRecentDeposits() {
    // 获取最近的充值记录
    const deposits = await this.binance.fetchDeposits();
    
    // 按时间倒序排列
    deposits.sort((a: any, b: any) => b.timestamp - a.timestamp);

    // 格式化返回数据
    return deposits.map((deposit: any) => ({
      id: deposit.id,
      currency: deposit.currency,
      amount: deposit.amount,
      status: deposit.status,
      timestamp: deposit.timestamp,
      txid: deposit.txid,
      network: deposit.network
    }));
  }

  /**
   * 根据交易哈希查询提现订单状态，2分钟内每5秒查询一次
   * @param txid 交易哈希
   * @returns 提现订单信息，如果超时未找到则返回null
   */
  async getWithdrawalByTxid(txid: string) {
    const timeout = 2 * 60 * 1000; // 2分钟超时
    const interval = 5000; // 5秒查询一次
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      // 获取最近的提现记录
      const withdrawals = await this.binance.fetchWithdrawals();
      
      // 查找匹配txid的提现记录
      const withdrawal = withdrawals.find((w: any) => w.txid === txid);
      
      if (withdrawal) {
        // 找到记录，返回格式化的提现记录
        return {
          id: withdrawal.id,
          txid: withdrawal.txid,
          currency: withdrawal.currency,
          amount: withdrawal.amount,
          status: withdrawal.status,
          timestamp: withdrawal.timestamp,
          network: withdrawal.network,
          address: withdrawal.address
        };
      }

      // 等待指定间隔
      await new Promise(resolve => setTimeout(resolve, interval));
    }

    // 超时返回null
    return null;
  }
  

  /**
   * 从 CEX 提现 SOL 到指定地址
   */
  async withdrawSol(amount: number, address: string) {
    const withdrawal = await this.binance.withdraw(
      "SOL",
      amount,
      address,
      undefined,
      { network: "SOL" }
    );
    return withdrawal;
  }

  /**
   * 获取 CEX IOTX 充值地址
   */
  async getIotxDepositAddress(): Promise<string> {
    const depositAddress = await this.binance.fetchDepositAddress('IOTX', {
      network: 'IOTX'
    });
    // console.log('🏦 获取到币安 IOTX 充值地址:', depositAddress.address);
    return depositAddress.address;
  }

  /**
   * get CEX SOL deposit address
   */
  async getSolDepositAddress(): Promise<string> {
    const depositAddress = await this.binance.fetchDepositAddress('SOL', {
      network: 'SOL'
    });
    // console.log('🏦 获取到币安 SOL 充值地址:', depositAddress.address);
    return depositAddress.address;
  }

  /**
   * 在 CEX 上卖出 IOTX 获得 USDT
   */
  async sellIotx(iotxAmount: number) {
    const order = await this.binance.createMarketSellOrder(
      "IOTXUSDT",
      iotxAmount
    );
    // console.log('💰 IOTX 卖单执行完成:', order.filled);
    return order;
  }

  async sellSymbol(symbol: string, amount: number) {
    const order = await this.binance.createMarketSellOrder(
      symbol,
      amount
    );
    // console.log(`💰 ${symbol} 卖单执行完成:`, order.filled);
    return order;
  }


  /**
   * 向 CEX 充值 IOTX（完整流程）
   */
  async depositIotx(iotxAmount: number, fromAddress: string) {
    // 1. 获取充值地址
    const depositAddress = await this.getIotxDepositAddress();
    
    // 2. 返回充值地址，实际转账由调用方处理
    return {
      depositAddress,
      amount: iotxAmount,
      fromAddress
    };
  }

  /**
   * 获取 CEX 账户 SOL 余额
   */
  async getSolBalance(): Promise<number> {
    const balance = await this.binance.fetchBalance();
    return balance.SOL?.free || 0;
  }

  /**
   * 获取 CEX 账户 USDT 余额
   */
  async getUsdtBalance(): Promise<number> {
    const balance = await this.binance.fetchBalance();
    return balance.USDT?.free || 0;
  }

  /**
   * 获取 CEX 账户 IOTX 余额
   */
  async getIotxBalance(): Promise<number> {
    const balance = await this.binance.fetchBalance();
    return balance.IOTX?.free || 0;
  }

  /**
   * 等待 CEX 上的 IOTX 余额增加
   * @param expectedAmount 预期增加的 IOTX 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 5 分钟
   * @param checkInterval 检查间隔（毫秒），默认 15 秒
   */
  async waitForIotxBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 300000, // 5 分钟
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 CEX IOTX 余额增加 ${expectedAmount} IOTX...`);

    // 获取初始余额
    const initialBalance = await this.getIotxBalance();

    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const currentBalance = await this.getIotxBalance();

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialBalance;
          console.log(`✅ CEX IOTX 余额已增加 ${actualIncrease.toFixed(2)} IOTX`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 CEX IOTX 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    const finalBalance = await this.getIotxBalance();
    const actualIncrease = finalBalance - initialBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到余额增加 ${actualIncrease.toFixed(2)} IOTX，继续执行...`);
    } else {
      throw new Error(`等待 CEX IOTX 到账超时，预期增加 ${expectedAmount} IOTX，实际增加 ${actualIncrease.toFixed(2)} IOTX`);
    }
  }

  /**
   * 等待 CEX 上的 SOL 余额增加
   * @param expectedAmount 预期增加的 SOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 5 分钟
   * @param checkInterval 检查间隔（毫秒），默认 15 秒
   */
  async waitForSolBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 300000, // 5 分钟
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 CEX SOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始余额
    const initialBalance = await this.getSolBalance();

    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const currentBalance = await this.getSolBalance();

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialBalance;
          console.log(`✅ CEX SOL 余额已增加 ${actualIncrease.toFixed(4)} SOL`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 CEX SOL 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    const finalBalance = await this.getSolBalance();
    const actualIncrease = finalBalance - initialBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到余额增加 ${actualIncrease.toFixed(4)} SOL，继续执行...`);
    } else {
      throw new Error(`等待 CEX SOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualIncrease.toFixed(4)} SOL`);
    }
  }
}
