// 简单的测试脚本来验证 poc.ts 的功能
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

console.log('🧪 开始测试套利系统...');

async function runTests() {
    try {
        // 测试 TypeScript 编译
        console.log('检查 TypeScript 编译...');
        await execAsync('npx tsc --noEmit --skipLibCheck poc.ts');
        console.log('✅ TypeScript 编译检查通过');
        
        // 如果编译通过，尝试运行帮助命令
        console.log('测试程序运行...');
        const { stdout } = await execAsync('npx ts-node poc.ts --help');
        console.log('✅ 程序运行测试通过');
        console.log('输出:');
        console.log(stdout);
        
    } catch (error) {
        console.log('❌ 测试失败:');
        console.log(error.message);
        if (error.stderr) {
            console.log('错误详情:');
            console.log(error.stderr);
        }
    }
}

runTests();