import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube-bridge";
import { SolanaService } from "./solana";
import { ArbitrageService } from "./arbitrage";
import dotenv from "dotenv";

// 加载环境变量
dotenv.config();

// 配置信息
const config = {
  binance: {
    apiKey:
      process.env.BINANCE_API_KEY ||
      "Ma577gWl6htZWNFsnRuQB9hJQHDNzboLrWuaLxHzTGE8v5fevBf9kOc4ofKKZ3e6",
    secret:
      process.env.BINANCE_SECRET ||
      "Hkcn9zY2vEVnAXbvELJzgrZmcsZ1VPxFvb8BGwhPHZHFa75QVHfhp1xHTNY1PaTl",
  },
  mimo: {
    dbUrl:
      process.env.MIMO_DB_URL ||
      "postgresql://mimo:<EMAIL>/mimo",
    rpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    routerV3: "0x17C1Ae82D99379240059940093762c5e4539aba5",
    privateKey: process.env.IOTEX_PRIVATE_KEY || 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a', // IoTeX 私钥
    tokens: {
      SOL: {
        address: "0xa1f3f211d9b33f2086a800842836d67f139b9a7a",
        symbol: "SOL",
        decimals: 9,
      },
      IOTX: {
        address: "IOTX", // Native token
        symbol: "IOTX",
        decimals: 18,
      },
      WIOTX: {
        address: "0xA00744882684C3e4747faEFD68D283eA44099D03",
        symbol: "WIOTX",
        decimals: 18,
      },
    },
  },
  bridge: {
    iotexRpcUrl:
      process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    solanaRpcUrl:
      process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    iotexPrivateKey: process.env.IOTEX_PRIVATE_KEY || 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a',
    solanaPrivateKey: process.env.SOLANA_PRIVATE_KEY || '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
    bridgeContractAddress: "******************************************", // 实际的桥接合约地址
  },
  solana: {
    rpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    privateKey: process.env.SOLANA_PRIVATE_KEY || '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
  },
  arbitrage: {
    maxTradeAmount: 100, // 最大交易金额 $100 (测试用)
    slippage: 1, // 1% 滑点容忍度
    walletAddresses: {
      iotex: process.env.IOTEX_WALLET_ADDRESS || "******************************************",
      solana: process.env.SOLANA_WALLET_ADDRESS || "57hfxmbXd2SC5YESxT2bL8mSG2adoV8UJpSWSSNCxJjK",
    },
  },
};

// 初始化服务
const cexService = new CexService(config.binance);
const mimoService = new MimoService(config.mimo.dbUrl, {
  rpcUrl: config.mimo.rpcUrl,
  privateKey: config.mimo.privateKey,
});
const bridgeService = new IoTubeBridgeService(config.bridge);
const solanaService = new SolanaService(config.solana);
const arbitrageService = new ArbitrageService(
  cexService,
  mimoService,
  bridgeService,
  solanaService,
  config.arbitrage
);

/**
 * 套利策略说明：
 * 1. 读取两边 SOL 的价格（MIMO SOL 的价格）（币安 SOL 的价格）
 * 2. 获取差距并判断执行方向
 *
 * CEX 价格大于 DEX 价格时：CEX -> DEX 套利
 *   - 在 CEX 上买 IOTX
 *   - 将 IOTX 提现到 IoTeX 钱包
 *   - 在 MIMO 将 IOTX 换成 SOL
 *   - 将 SOL 通过跨链桥转移到 Solana
 *   - 在 CEX 上卖出 SOL 获得 USDT
 *
 * DEX 价格大于 CEX 价格时：DEX -> CEX 套利
 *   - 在 CEX 上买 SOL
 *   - 将 SOL 提现到 Solana 账户
 *   - 通过 IoTube Bridge 将 SOL 转移到 IoTeX
 *   - 在 MIMO 上将 SOL 转换为 IOTX
 *   - 将 IOTX 充值到 CEX 并卖出获得 USDT
 */

/**
 * 初始化所有服务
 */
export async function initializeServices() {
  console.log("🔧 初始化服务...");

  try {
    // 初始化 MIMO 服务
    if (mimoService.isWalletInitialized()) {
      await mimoService.initialize();
      console.log("✅ MIMO 服务初始化完成");
    } else {
      console.log("⚠️  MIMO 服务运行在只读模式（未提供私钥）");
    }

    // 初始化 Solana 服务
    if (solanaService.isWalletInitialized()) {
      await solanaService.initialize();
      console.log("✅ Solana 服务初始化完成");
    } else {
      console.log("⚠️  Solana 服务运行在只读模式（未提供私钥）");
    }

    console.log("✅ 所有服务初始化完成");
  } catch (error) {
    console.error("❌ 服务初始化失败:", error);
    throw error;
  }
}

/**
 * 主函数 - 执行套利分析和交易
 */
export async function main() {
  try {
    console.log("🚀 启动套利系统...");
    console.log("配置信息:", {
      最大交易金额: `$${config.arbitrage.maxTradeAmount}`,
      滑点容忍度: `${config.arbitrage.slippage}%`,
    });

    // 初始化服务
    await initializeServices();
    await arbitrageService.executeCexToDexArbitrage(20);
    process.exit(0);

    // 分析套利机会
    console.log("\n🔍 执行实时套利分析...");
    const opportunity = await arbitrageService.analyzeArbitrageOpportunity();

    if (opportunity.profitable) {
      console.log(
        `\n✅ 发现套利机会! 汇率差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX`
      );

      // 在测试环境中，使用小金额
      const tradeAmount = 1000;
      console.log(`准备执行套利，金额: $${tradeAmount}`);

      // 注意：在实际环境中才执行真实交易
      if (
        process.env.NODE_ENV === "production" &&
        mimoService.isWalletInitialized()
      ) {
        const result = await arbitrageService.executeArbitrage(tradeAmount);
        if (result) {
          console.log("🎉 套利执行完成:", result);
        }
      } else {
        console.log("💡 模拟模式：跳过实际交易执行");
        console.log("💡 要执行真实交易，请设置 NODE_ENV=production 并配置私钥");
      }
    } else {
      console.log("\n❌ 当前没有盈利的套利机会");
      console.log(
        `汇率差异: ${opportunity.adjustedRateDifference.toFixed(2)} IOTX`
      );
    }
  } catch (error) {
    console.error("❌ 套利系统执行失败:", error);
  } finally {
    // 关闭数据库连接
    await mimoService.close();
  }
}

/**
 * 监控模式 - 持续监控套利机会
 */
export async function startMonitoring() {
  console.log("🔄 启动套利监控模式...");
  await arbitrageService.monitorArbitrageOpportunities(30000); // 每30秒检查一次
}

// 主执行逻辑
if (require.main === module) {
  main().catch(console.error);
}