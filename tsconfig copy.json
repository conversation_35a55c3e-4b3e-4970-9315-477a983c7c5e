{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "types": ["bun-types"], "lib": ["ES2022"], "allowImportingTsExtensions": true, "noEmit": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}