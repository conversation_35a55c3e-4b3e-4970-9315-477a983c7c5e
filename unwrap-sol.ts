import bs58 from "bs58";
import {
  Connection,
  Keypair,
  PublicKey,
  Transaction,
  sendAndConfirmTransaction,
} from "@solana/web3.js";
import { createCloseAccountInstruction, TOKEN_PROGRAM_ID } from "@solana/spl-token";

export async function wsolUnwrap(pkBase58: string) {
  const secretKey = bs58.decode(pkBase58);
  const payer = Keypair.fromSecretKey(Uint8Array.from(secretKey));

  // 2. 建立连接（主网 Beta）
  const connection = new Connection("https://api.mainnet-beta.solana.com", "confirmed");

  // 3. 定位所有 WSOL 关联账户
  const WSOL_MINT = new PublicKey("So11111111111111111111111111111111111111112");
  const resp = await connection.getTokenAccountsByOwner(payer.publicKey, {
    mint: WSOL_MINT,
  });

  if (resp.value.length === 0) {
    console.log("🔍 账户下没有找到任何 WSOL 关联账户。");
    return;
  }

  // 4. 构造关闭指令
  const tx = new Transaction();
  for (const { pubkey, account } of resp.value) {
    // Account data layout: offset 64–72 存储 token amount（u64 LE）
    const amount = Number(account.data.readBigUInt64LE(64));
    if (amount === 0) continue;

    tx.add(
      createCloseAccountInstruction(
        pubkey,            // 要关闭的 WSOL token 账户
        payer.publicKey,   // SOL 收款方（解封后的 SOL 回到这里）
        payer.publicKey    // 账户拥有者
      )
    );
  }

  if (tx.instructions.length === 0) {
    console.log("✅ 所有 WSOL 账户均为空，无需执行 unwrap。");
    return;
  }

  // 5. 发送并确认交易
  const sig = await sendAndConfirmTransaction(connection, tx, [payer]);
  console.log("🚀 Unwrap 成功，交易签名：", sig);
}

await wsolUnwrap('2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf');