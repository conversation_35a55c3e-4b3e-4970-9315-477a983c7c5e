# 余额检测改进文档

## 概述

本次改进将套利系统中的固定等待时间替换为智能的余额检测机制，提高了系统的效率和可靠性。

## 改进内容

### 1. 新增的余额检测方法

#### ArbitrageService 中的链上余额检测方法

##### `waitForSolBalanceIncrease()`
- **功能**: 检测 IoTeX 上的 SOL (WSOL) 代币余额增加
- **参数**:
  - `expectedAmount`: 预期增加的 SOL 数量
  - `maxWaitTime`: 最大等待时间（默认 5 分钟）
  - `checkInterval`: 检查间隔（默认 10 秒）
- **使用场景**: 跨链桥从 Solana 转移 SOL 到 IoTeX 后的等待

##### `waitForIotxBalanceIncrease()`
- **功能**: 检测 IoTeX 上的 IOTX 余额增加
- **参数**:
  - `expectedAmount`: 预期增加的 IOTX 数量
  - `maxWaitTime`: 最大等待时间（默认 3 分钟）
  - `checkInterval`: 检查间隔（默认 10 秒）
- **使用场景**: CEX 提现 IOTX 到 IoTeX 钱包后的等待

##### `waitForSolanaBalanceIncrease()`
- **功能**: 检测 Solana 上的 SOL 余额增加
- **参数**:
  - `expectedAmount`: 预期增加的 SOL 数量
  - `maxWaitTime`: 最大等待时间（默认 5 分钟）
  - `checkInterval`: 检查间隔（默认 10 秒）
- **使用场景**: 跨链桥从 IoTeX 转移 SOL 到 Solana 后的等待

#### CexService 中的 CEX 余额检测方法

##### `waitForIotxBalanceIncrease()`
- **功能**: 检测 CEX 上的 IOTX 余额增加
- **参数**:
  - `expectedAmount`: 预期增加的 IOTX 数量
  - `maxWaitTime`: 最大等待时间（默认 5 分钟）
  - `checkInterval`: 检查间隔（默认 15 秒）
- **使用场景**: 从链上转账 IOTX 到 CEX 充值地址后的等待

##### `waitForSolBalanceIncrease()`
- **功能**: 检测 CEX 上的 SOL 余额增加
- **参数**:
  - `expectedAmount`: 预期增加的 SOL 数量
  - `maxWaitTime`: 最大等待时间（默认 5 分钟）
  - `checkInterval`: 检查间隔（默认 15 秒）
- **使用场景**: 从链上转账 SOL 到 CEX 充值地址后的等待

### 2. 替换的固定等待时间

#### 策略 1: CEX → DEX 套利
- **原来**: `await new Promise((resolve) => setTimeout(resolve, 60000));` (60秒固定等待)
- **现在**: `await this.waitForIotxBalanceIncrease(iotxAmount);` (智能检测 IOTX 余额)

- **原来**: `await new Promise((resolve) => setTimeout(resolve, 180000));` (180秒固定等待)
- **现在**: `await this.waitForSolanaBalanceIncrease(bridgeResult.receivedAmount);` (智能检测 Solana SOL 余额)

#### 策略 2: DEX → CEX 套利
- **原来**: `await new Promise((resolve) => setTimeout(resolve, 40_000));` (40秒固定等待)
- **现在**: `await this.waitForSolanaBalanceIncrease(solAmount);` (智能检测 Solana SOL 余额)

- **原来**: `await new Promise((resolve) => setTimeout(resolve, 180_000));` (180秒固定等待)
- **现在**: `await this.waitForSolBalanceIncrease(bridgeResult.receivedAmount);` (智能检测 IoTeX SOL 余额)

#### CEX 充值到账检测
- **原来**: `await this.cexService.getWithdrawalByTxid(transferTx);` (通过交易哈希查询)
- **现在**: `await this.cexService.waitForSolBalanceIncrease(swapResult.solAmount);` (智能检测 CEX SOL 余额)

- **原来**: `await new Promise((resolve) => setTimeout(resolve, 60000));` (60秒固定等待)
- **现在**: `await this.cexService.waitForIotxBalanceIncrease(swapResult.iotxAmount);` (智能检测 CEX IOTX 余额)

### 3. 保留的固定等待时间

以下等待时间被保留，因为它们有特定的用途：

1. **订单处理缓冲时间**:
   - `await new Promise((resolve) => setTimeout(resolve, 10000));` (10秒)
   - `await new Promise((resolve) => setTimeout(resolve, 5_000));` (5秒)
   - 用途: 确保 CEX 订单完全处理完成

2. **CEX 充值等待**:
   - `await new Promise((resolve) => setTimeout(resolve, 60000));` (60秒)
   - 用途: 等待 IOTX 充值到 CEX 到账
   - 原因: 暂时没有直接的 CEX 余额检测 API

## 技术实现细节

### 余额检测逻辑

1. **获取初始余额**: 在开始等待前记录当前余额
2. **设置目标余额**: 初始余额 + 预期增加量 × 0.95 (考虑手续费损失)
3. **循环检测**: 每隔指定时间检查一次余额
4. **超时处理**: 达到最大等待时间后进行最终检查
5. **容错机制**: 如果检测到任何余额增加，即使未达到目标也继续执行

### 错误处理

- **网络错误**: 捕获余额查询异常，继续重试
- **超时处理**: 提供详细的错误信息，包括预期和实际的余额变化
- **部分到账**: 如果检测到部分余额增加，记录警告但继续执行

## 优势

1. **提高效率**: 不再需要等待固定的最大时间，一旦检测到余额变化立即继续
2. **增强可靠性**: 实际检测余额变化，而不是盲目等待
3. **更好的监控**: 提供详细的余额变化日志
4. **灵活配置**: 可以调整等待时间和检查间隔
5. **容错能力**: 处理网络异常和部分到账情况

## 使用示例

```typescript
// 等待 IoTeX 上的 SOL 余额增加 0.5 SOL
await this.waitForSolBalanceIncrease(0.5);

// 等待 IOTX 余额增加 100 IOTX，最多等待 2 分钟
await this.waitForIotxBalanceIncrease(100, 120000);

// 等待 Solana SOL 余额增加，每 5 秒检查一次
await this.waitForSolanaBalanceIncrease(0.3, 300000, 5000);
```

## 测试

创建了 `test/balance-detection.test.ts` 文件来测试余额检测功能：

```bash
# 运行余额检测测试
npx ts-node test/balance-detection.test.ts
```

## 注意事项

1. **私钥安全**: 确保所有私钥都通过环境变量安全配置
2. **网络稳定性**: 余额检测依赖于网络连接，需要处理网络异常
3. **手续费考虑**: 目标余额设置为 95% 以考虑可能的手续费损失
4. **超时设置**: 根据实际网络情况调整超时时间

## 未来改进

1. **CEX 余额检测**: 集成 CEX API 来检测充值到账状态
2. **动态调整**: 根据网络拥堵情况动态调整检查间隔
3. **更精确的手续费计算**: 基于实际交易数据优化手续费预估
4. **并行检测**: 同时检测多个地址的余额变化
