# 套利策略说明

## 概述

系统支持两种套利策略，根据 CEX 和 DEX 之间的价格差异自动选择最优策略。

## 策略详解

### 策略一：CEX买入 -> DEX卖出 (`buy_cex_sell_dex`)

**触发条件：** CEX 上 SOL 价格 **低于** DEX 上 SOL 价格

**套利逻辑：** 在便宜的地方买，在贵的地方卖

**完整流程：**
```
1. CEX 买入 SOL (用 USDT)
2. 将 SOL 从 CEX 提现到 Solana 钱包
3. 通过跨链桥将 SOL 转到 IoTeX (变成 ioSOL)
4. 在 MIMO DEX 上用 ioSOL 换取 IOTX
5. 将 IOTX 充值到 CEX
6. 在 CEX 上卖出 IOTX 换取 USDT
```

**示例：**
- CEX: 1 SOL = 6000 IOTX (SOL 便宜)
- DEX: 1 SOL = 6100 IOTX (SOL 贵)
- 差异: 100 IOTX (有套利空间)

### 策略二：DEX买入 -> CEX卖出 (`buy_dex_sell_cex`)

**触发条件：** DEX 上 SOL 价格 **低于** CEX 上 SOL 价格

**套利逻辑：** 在便宜的地方买，在贵的地方卖

**完整流程：**
```
1. CEX 买入 IOTX (用 USDT)
2. 将 IOTX 从 CEX 提现到 IoTeX 钱包
3. 在 MIMO DEX 上用 IOTX 换取 ioSOL
4. 通过跨链桥将 ioSOL 转到 Solana (变成 SOL)
5. 将 SOL 充值到 CEX
6. 在 CEX 上卖出 SOL 换取 USDT
```

**示例：**
- DEX: 1 SOL = 6000 IOTX (SOL 便宜)
- CEX: 1 SOL = 6100 IOTX (SOL 贵)
- 差异: 100 IOTX (有套利空间)

## 方法命名说明

### 新的清晰命名

- `executeBuyOnCexSellOnDex()` - CEX买入，DEX卖出
- `executeBuyOnDexSellOnCex()` - DEX买入，CEX卖出

### 方向标识

- `buy_cex_sell_dex` - 在CEX买入，在DEX卖出
- `buy_dex_sell_cex` - 在DEX买入，在CEX卖出

## 费用计算

### MIMO DEX 手续费

- **费率：** 0.3% (可通过 `MIMO_FEE_RATE` 环境变量配置)
- **计算方式：** 按交易输出金额计算

### 策略一费用计算
```javascript
// CEX买入 -> DEX卖出: 在 DEX 用 ioSOL 换 IOTX 时扣费
const feeInIotx = dexSolToIotxRate * mimoFeeRate;
adjustedRateDifference = rateDifference - feeInIotx;
```

### 策略二费用计算
```javascript
// DEX买入 -> CEX卖出: 在 DEX 用 IOTX 换 ioSOL 时扣费
const feeInIotx = cexSolToIotxRate * mimoFeeRate;
adjustedRateDifference = rateDifference - feeInIotx;
```

## 自动策略选择

系统会自动分析价格差异并选择最优策略：

```javascript
// 确定套利方向
const direction = cexSolToIotxRate > dexSolToIotxRate ? "buy_cex_sell_dex" : "buy_dex_sell_cex";
```

**逻辑：**
- 如果 CEX 汇率 > DEX 汇率 → CEX 上 SOL 贵 → 选择 "CEX买入 -> DEX卖出"
- 如果 DEX 汇率 > CEX 汇率 → DEX 上 SOL 贵 → 选择 "DEX买入 -> CEX卖出"

## 风险提示

1. **跨链风险：** 跨链转账可能失败或延迟
2. **价格波动：** 执行过程中价格可能发生变化
3. **手续费：** 各环节都有手续费，需要计算净利润
4. **流动性风险：** DEX 流动性不足可能导致滑点过大

## 监控输出示例

```
🔍 分析套利机会...
💰 CEX: 1 SOL = 6151.47 IOTX | DEX: 1 SOL = 6193.78 IOTX | 差异: 42.31 IOTX
扣除 MIMO 0.3% 手续费后的差异: 23.85 IOTX

📊 套利分析结果:
套利方向: buy_dex_sell_cex (DEX买入 -> CEX卖出)
调整后差异: 23.85 IOTX
```

这样的命名让你一眼就能看出：
- 在哪里买入 (CEX/DEX)
- 在哪里卖出 (DEX/CEX)
- 具体的执行流程和方向
