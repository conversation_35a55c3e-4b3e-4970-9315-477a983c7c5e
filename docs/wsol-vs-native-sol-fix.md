# WSOL vs Native SOL 余额检测修复

## 问题描述

用户指出了一个关键问题：跨链转账发送过去的是 **WSOL (Wrapped SOL)**，而不是 **native SOL**。但是我们的余额检测方法 `waitForSolanaBalanceIncrease` 检查的是 native SOL 余额，这导致无法检测到跨链转账的到账。

## 问题分析

### 1. 跨链转账 vs CEX 提现的区别

**跨链转账（Bridge）**:
- 从 IoTeX 转账到 Solana 时，接收到的是 **WSOL**
- WSOL 是 SOL 的 SPL Token 版本，需要通过 Token Program 管理
- 余额存储在 Token Account 中，不是钱包的 native balance

**CEX 提现**:
- 从 CEX 提现到 Solana 时，接收到的是 **native SOL**
- Native SOL 直接存储在钱包地址中
- 可以通过 `connection.getBalance()` 直接查询

### 2. 原来的检测方法问题

```typescript
// 原来的方法只检测 native SOL
async getSolBalance(): Promise<number> {
  const balance = await this.connection.getBalance(publicKey);
  return balance / LAMPORTS_PER_SOL;
}
```

这个方法无法检测到 WSOL 余额变化。

## 解决方案

### 1. 新增 WSOL 余额检测方法

在 `solana.ts` 中添加了专门的 WSOL 余额检测：

```typescript
/**
 * 获取 WSOL (Wrapped SOL) 余额
 */
async getWsolBalance(walletAddress?: string): Promise<number> {
  const publicKey = new PublicKey(address);
  // WSOL mint 地址
  const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
  
  // 获取所有 WSOL 关联账户
  const resp = await this.connection.getTokenAccountsByOwner(publicKey, {
    mint: WSOL_MINT,
  });

  let totalWsolBalance = 0;
  
  for (const { account } of resp.value) {
    // Account data layout: offset 64–72 存储 token amount（u64 LE）
    const amount = Number(account.data.readBigUInt64LE(64));
    totalWsolBalance += amount;
  }

  // 转换为 SOL 单位
  return totalWsolBalance / LAMPORTS_PER_SOL;
}
```

### 2. 新增总余额检测方法

```typescript
/**
 * 获取总 SOL 余额（native SOL + WSOL）
 */
async getTotalSolBalance(walletAddress?: string): Promise<{ 
  native: number; 
  wsol: number; 
  total: number 
}> {
  const nativeBalance = await this.getSolBalance(walletAddress);
  const wsolBalance = await this.getWsolBalance(walletAddress);
  
  return {
    native: nativeBalance,
    wsol: wsolBalance,
    total: nativeBalance + wsolBalance
  };
}
```

### 3. 区分不同场景的余额检测

在 `arbitrage.ts` 中创建了两个不同的方法：

#### 跨链转账场景 - 检测 WSOL 余额
```typescript
/**
 * 等待 Solana 上的 WSOL 余额增加（跨链转账接收的是 WSOL）
 */
private async waitForSolanaWsolBalanceIncrease(
  expectedAmount: number,
  maxWaitTime: number = 600000, // 10 分钟
  checkInterval: number = 15000
): Promise<void> {
  // 检测 WSOL 余额变化
  const currentBalances = await this.solanaService.getTotalSolBalance();
  
  if (currentBalances.wsol >= targetWsolBalance) {
    const actualIncrease = currentBalances.wsol - initialWsolBalance;
    console.log(`✅ Solana WSOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
    return;
  }
}
```

#### CEX 提现场景 - 检测 native SOL 余额
```typescript
/**
 * 等待 Solana 上的 native SOL 余额增加（CEX 提现接收的是 native SOL）
 */
private async waitForSolanaBalanceIncrease(
  expectedAmount: number,
  maxWaitTime: number = 300000, // 5 分钟
  checkInterval: number = 15000
): Promise<void> {
  // 检测 native SOL 余额变化
  const currentBalance = await this.solanaService.getSolBalance();
  
  if (currentBalance >= targetBalance) {
    const actualIncrease = currentBalance - initialNativeBalance;
    console.log(`✅ Solana native SOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
    return;
  }
}
```

### 4. 更新调用方式

**跨链转账调用**:
```typescript
// IoTeX → Solana 跨链转账后，检测 WSOL 余额
await this.waitForSolanaWsolBalanceIncrease(bridgeResult.receivedAmount);
```

**CEX 提现调用**:
```typescript
// CEX 提现到 Solana 后，检测 native SOL 余额
await this.waitForSolanaBalanceIncrease(solAmount);
```

## 技术细节

### WSOL Token Account 结构

WSOL 作为 SPL Token，其余额存储在 Token Account 中：

```
Token Account Data Layout:
- Offset 0-32:   Mint address (32 bytes)
- Offset 32-64:  Owner address (32 bytes)  
- Offset 64-72:  Amount (u64, little-endian) ← 这里是余额
- Offset 72-73:  Delegate option (1 byte)
- ...
```

### WSOL Mint 地址

WSOL 的官方 mint 地址是固定的：
```
So11111111111111111111111111111111111111112
```

### 余额读取方法

```typescript
// 从 Token Account 数据中读取余额
const amount = Number(account.data.readBigUInt64LE(64));
```

## 修复效果

### 1. 准确的余额检测

现在系统可以正确区分和检测：
- **跨链转账**: 检测 WSOL 余额变化
- **CEX 提现**: 检测 native SOL 余额变化

### 2. 详细的余额信息

新的日志输出提供更详细的信息：

```
📊 初始 Solana 余额: Native SOL: 0.5 SOL, WSOL: 0.0 SOL
🔍 第 1 次检查 (15s): Native: 0.5 SOL, WSOL: 0.112 SOL
✅ Solana WSOL 余额已增加 0.112000 SOL
```

### 3. 更合理的等待时间

- **跨链转账**: 10 分钟（需要更多确认）
- **CEX 提现**: 5 分钟（相对较快）

## 注意事项

### 1. Unwrap 操作

在使用 WSOL 之前，通常需要先 unwrap 为 native SOL：

```typescript
// 在套利流程中，解包 WSOL 为 native SOL
await solanaService.unwrapSol();
```

### 2. 手续费考虑

- Native SOL 转账手续费从 native SOL 余额扣除
- WSOL 转账手续费也从 native SOL 余额扣除
- 需要确保钱包有足够的 native SOL 支付手续费

### 3. 错误处理

新增了针对 Token Account 查询失败的错误处理：

```typescript
try {
  const resp = await this.connection.getTokenAccountsByOwner(publicKey, {
    mint: WSOL_MINT,
  });
} catch (error) {
  console.error('Failed to get WSOL balance:', error);
  throw error;
}
```

## 总结

这次修复解决了跨链转账余额检测的根本问题：

- ✅ **正确区分 WSOL 和 native SOL**
- ✅ **针对不同场景使用正确的检测方法**
- ✅ **提供详细的余额变化信息**
- ✅ **更准确的到账检测**

现在系统可以正确检测跨链转账的 WSOL 到账，避免了之前"还没开始等就超时"的问题。
