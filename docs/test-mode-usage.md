# 测试模式使用指南

## 概述

测试模式是一个专门设计的功能，允许你在不考虑实际盈利条件的情况下测试完整的套利流程。这对于验证系统功能、调试问题和学习套利流程非常有用。

## 功能特点

### 🧪 忽略盈利条件
- 无论价格差异多小，都会执行套利流程
- 适合在市场条件不佳时测试系统

### 💰 可配置交易金额
- 默认使用 $20 进行测试
- 可通过环境变量自定义金额

### 🔄 循环执行
- 支持设定循环次数和间隔时间
- 可以无限循环或指定次数

### 🛡️ 安全模式
- 在开发环境中只进行模拟交易
- 在生产环境中执行真实交易（需谨慎）

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 测试模式配置
TEST_MODE=true            # 启用测试模式
TEST_TRADE_AMOUNT=20      # 测试交易金额 ($)
TEST_CYCLE_INTERVAL=300   # 测试循环间隔 (秒)
TEST_MAX_CYCLES=10        # 最大循环次数 (0=无限循环)
```

### 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `TEST_MODE` | `false` | 是否启用测试模式 |
| `TEST_TRADE_AMOUNT` | `20` | 测试时使用的交易金额（美元） |
| `TEST_CYCLE_INTERVAL` | `300` | 每轮测试之间的间隔时间（秒） |
| `TEST_MAX_CYCLES` | `10` | 最大测试轮数，0表示无限循环 |

## 使用方法

### 1. 快速启动测试

```bash
# 使用默认配置启动测试模式
bun run start:test

# 或者
bun run start.ts --test
```

### 2. 自定义配置测试

编辑 `.env` 文件：
```bash
TEST_MODE=true
TEST_TRADE_AMOUNT=50      # 使用 $50 测试
TEST_CYCLE_INTERVAL=180   # 每 3 分钟一轮
TEST_MAX_CYCLES=5         # 总共 5 轮
```

然后启动：
```bash
bun run start:test
```

### 3. 无限循环测试

```bash
# 在 .env 中设置
TEST_MAX_CYCLES=0

# 启动后将持续运行，直到手动停止 (Ctrl+C)
bun run start:test
```

## 测试流程

### 典型的测试轮次包括：

1. **价格分析** - 获取 CEX 和 DEX 价格
2. **机会评估** - 计算价格差异（测试模式下忽略盈利条件）
3. **方向确定** - 决定 CEX→DEX 还是 DEX→CEX
4. **交易执行** - 根据环境执行真实或模拟交易
5. **结果报告** - 显示执行结果和统计信息
6. **等待间隔** - 等待指定时间后开始下一轮

### 示例输出

```
🧪 启动测试模式
📊 配置: 交易金额 $20, 循环间隔 300秒, 最大循环 10次
⚠️  测试模式将忽略盈利条件，强制执行套利流程

🔄 第 1 轮测试 / 10
==================================================
🔍 分析套利机会...
💰 CEX: 1 SOL = 6346.70 IOTX | DEX: 1 SOL = 6303.68 IOTX | 差异: 43.03 IOTX
扣除 MIMO 0.3% 手续费后的差异: 24.12 IOTX
🎯 测试模式：强制执行套利 (实际差异: 24.12 IOTX)
🧪 测试模式：执行套利交易 (金额: $20)
📊 套利方向: cex_to_dex
💰 价格差异: 24.12 IOTX
💡 开发模式：模拟交易执行
✅ 模拟 CEX 买入
✅ 模拟跨链转账
✅ 模拟 DEX 交换
🎉 模拟套利完成: {
  方向: "cex_to_dex",
  投入: "$20",
  估算利润: "2.41 IOTX"
}
✅ 第 1 轮测试完成
⏳ 等待 300 秒后开始下一轮...
```

## 安全注意事项

### 🔒 开发环境 vs 生产环境

- **开发环境** (`NODE_ENV=development`)：只进行模拟交易，不会消耗真实资金
- **生产环境** (`NODE_ENV=production`)：执行真实交易，会消耗真实资金

### ⚠️ 生产环境使用警告

在生产环境中使用测试模式时要格外小心：

1. **资金风险**：测试模式会忽略盈利条件，可能导致亏损交易
2. **频繁交易**：循环执行可能产生大量交易费用
3. **市场影响**：频繁的小额交易可能影响你的交易历史

### 💡 建议的使用场景

**适合使用测试模式的情况：**
- 验证系统功能是否正常
- 测试新的配置参数
- 学习套利流程
- 调试问题

**不建议使用测试模式的情况：**
- 生产环境的日常运行
- 大额资金的交易
- 市场条件良好时（应使用正常模式）

## 停止测试

### 手动停止
按 `Ctrl+C` 可以随时停止测试模式。

### 自动停止
当达到设定的最大循环次数时，测试会自动停止。

## 故障排除

### 常见问题

1. **测试模式未启用**
   - 检查 `.env` 文件中的 `TEST_MODE=true`
   - 或者使用 `--test` 参数会临时启用

2. **配置验证失败**
   - 运行 `bun run validate-config.ts` 检查配置
   - 确保所有必需的环境变量都已设置

3. **服务连接失败**
   - 检查网络连接
   - 验证 API 密钥和数据库连接

### 调试技巧

1. **查看详细日志**：测试模式会输出详细的执行信息
2. **单步调试**：设置 `TEST_MAX_CYCLES=1` 进行单次测试
3. **调整间隔**：使用较短的 `TEST_CYCLE_INTERVAL` 快速测试

## 总结

测试模式是一个强大的工具，可以帮助你：
- ✅ 验证系统功能
- ✅ 学习套利流程  
- ✅ 调试和优化
- ✅ 安全地测试配置

记住在生产环境中谨慎使用，并始终监控测试结果！
