# 简化的套利分析逻辑

## 概述

根据用户要求，我们简化了套利分析逻辑：
1. 不使用百分比计算，直接比较 IOTX 数量差异
2. 只需要检查差价是否超过 50 IOTX
3. 扣除 MIMO v3 的 0.3% 交易费用
4. 不考虑跨链费用

## 核心逻辑

### 1. 计算兑换率

```typescript
// CEX 兑换率：SOL 价格 / IOTX 价格
const cexSolToIotxRate = cexSolPrice / iotxPrice;

// DEX 兑换率：通过 MIMO API 获取
const dexSolToIotxRate = await this.mimoService.getSolToIotxRate(1);

// 计算差异（IOTX 数量）
const rateDifference = Math.abs(cexSolToIotxRate - dexSolToIotxRate);
```

### 2. 扣除交易费用

```typescript
const mimoFeeRate = 0.003; // MIMO v3 的 0.3% 手续费

if (direction === "cex_to_dex") {
  // CEX -> DEX: 在 DEX 用 SOL 换 IOTX，手续费按输出的 IOTX 计算
  const feeInIotx = dexSolToIotxRate * mimoFeeRate;
  adjustedRateDifference = rateDifference - feeInIotx;
} else {
  // DEX -> CEX: 在 DEX 用 IOTX 换 SOL，手续费按输入的 IOTX 计算
  const feeInIotx = cexSolToIotxRate * mimoFeeRate;
  adjustedRateDifference = rateDifference - feeInIotx;
}
```

### 3. 判断套利机会

```typescript
// 简单判断：差价超过 50 IOTX 就有套利机会
const profitable = adjustedRateDifference > 50;
```

## 示例计算

假设当前价格：
- CEX SOL 价格：$181.52
- IOTX 价格：$0.02814
- DEX SOL → IOTX 汇率：6466.6 IOTX

### 计算过程

1. **CEX 兑换率**：
   ```
   $181.52 / $0.02814 = 6,451.6 IOTX per SOL
   ```

2. **差异**：
   ```
   |6,451.6 - 6,466.6| = 15.0 IOTX
   ```

3. **扣除手续费**（假设 DEX → CEX 方向）：
   ```
   手续费 = 6,451.6 × 0.3% = 19.4 IOTX
   调整后差异 = 15.0 - 19.4 = -4.4 IOTX
   ```

4. **判断结果**：
   ```
   -4.4 IOTX < 50 IOTX → 无套利机会
   ```

## 日志输出

### 新的简化日志

```
💰 CEX: 1 SOL = 6451.60 IOTX | DEX: 1 SOL = 6466.62 IOTX | 差异: 15.00 IOTX
扣除 MIMO 0.3% 手续费后的差异: -4.40 IOTX
❌ 当前没有盈利的套利机会
汇率差异: -4.40 IOTX
最小要求差异: 50 IOTX
```

### 有套利机会时的日志

```
💰 CEX: 1 SOL = 6400.00 IOTX | DEX: 1 SOL = 6500.00 IOTX | 差异: 100.00 IOTX
扣除 MIMO 0.3% 手续费后的差异: 80.50 IOTX
🎯 发现套利机会! 方向: dex_to_cex, 差异: 80.50 IOTX
```

## 优势

### 1. 简单直观
- **无复杂计算**：直接比较 IOTX 数量
- **固定阈值**：50 IOTX 的简单判断标准
- **统一单位**：所有费用都用 IOTX 计算

### 2. 快速判断
- **无百分比转换**：减少计算步骤
- **直接对比**：一目了然的数量差异
- **即时决策**：快速判断是否执行套利

### 3. 实用性强
- **考虑实际费用**：扣除 MIMO 交易费
- **忽略小额费用**：不考虑跨链费用简化逻辑
- **专注核心**：只关注主要的盈利空间

## 配置调整

由于简化了逻辑，相关配置也需要调整：

```typescript
const config = {
  arbitrage: {
    minIotxDifference: 50, // 最小 IOTX 差异要求
    // 移除 minPriceDifference（百分比）
  }
};
```

## 代码变更总结

### 移除的功能
- ❌ 百分比差异计算
- ❌ 跨链费用考虑
- ❌ 复杂的费用转换

### 保留的功能
- ✅ 兑换率计算
- ✅ 套利方向判断
- ✅ MIMO 交易费扣除
- ✅ 简单的盈利判断

### 新增的功能
- ✅ 统一的 IOTX 费用计算
- ✅ 固定的 50 IOTX 阈值判断
- ✅ 简化的日志输出

## 使用建议

1. **监控频率**：由于逻辑简化，可以提高监控频率
2. **阈值调整**：根据实际情况调整 50 IOTX 的阈值
3. **费用更新**：定期确认 MIMO v3 的手续费率是否有变化

这种简化的方法更适合快速决策和自动化执行，减少了复杂的计算逻辑，专注于核心的套利机会识别。
