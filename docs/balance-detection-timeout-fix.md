# 余额检测超时问题修复

## 问题描述

用户报告在执行跨链套利时，`waitForSolanaBalanceIncrease` 方法几乎立即超时，显示：

```
⏳ 等待 Solana SOL 余额增加 0.112055628204 SOL...
❌ 等待 Solana SOL 到账超时，预期增加 0.112055628204 SOL，实际增加 0.0000 SOL
```

感觉还没开始等就提示超时了。

## 问题分析

经过代码检查，发现了以下问题：

### 1. 参数传递错误
在 `arbitrage.ts` 第 347 行：
```typescript
await this.waitForSolanaBalanceIncrease(bridgeResult.receivedAmount, 600);
```

这里传递的 `600` 被当作毫秒处理，实际只等待了 0.6 秒，而不是预期的 10 分钟。

### 2. 缺乏调试信息
原来的方法没有足够的调试信息来了解等待过程中发生了什么。

### 3. 跨链转账时间估计不足
跨链转账通常需要更长时间，5 分钟可能不够。

## 修复方案

### 1. 修复参数传递问题

**修复前**:
```typescript
await this.waitForSolanaBalanceIncrease(bridgeResult.receivedAmount, 600);
```

**修复后**:
```typescript
await this.waitForSolanaBalanceIncrease(bridgeResult.receivedAmount, 1000 * 60 * 10); // 10 分钟
```

或者使用默认参数：
```typescript
await this.waitForSolanaBalanceIncrease(bridgeResult.receivedAmount); // 使用默认 10 分钟
```

### 2. 增加详细的调试信息

**修复前的简单日志**:
```typescript
console.log(`⏳ 等待 Solana SOL 余额增加 ${expectedAmount} SOL...`);
// ... 等待过程中没有输出
console.log(`✅ Solana SOL 余额已增加 ${actualIncrease.toFixed(4)} SOL`);
```

**修复后的详细日志**:
```typescript
console.log(`⏳ 等待 Solana SOL 余额增加 ${expectedAmount} SOL...`);
console.log(`📊 初始 Solana SOL 余额: ${initialBalance} SOL`);
console.log(`🎯 目标余额: ${targetBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

// 等待过程中的详细日志
console.log(`🔍 第 ${checkCount} 次检查 (${elapsedTime}s): 当前余额 ${currentBalance} SOL`);
console.log(`⏳ 余额未达到目标，${checkInterval / 1000} 秒后重新检查 (剩余 ${remainingTime}s)`);

// 最终检查
console.log("⏰ 达到最大等待时间，进行最终检查...");
console.log(`📊 最终余额: ${finalBalance} SOL, 实际增加: ${actualIncrease.toFixed(6)} SOL`);
```

### 3. 延长等待时间和调整检查间隔

**修复前**:
- 最大等待时间: 5 分钟 (300,000 毫秒)
- 检查间隔: 10 秒

**修复后**:
- 最大等待时间: 10 分钟 (600,000 毫秒)
- 检查间隔: 15 秒 (减少 API 调用频率)

### 4. 改进错误处理

**新增功能**:
- 初始余额获取失败的错误处理
- 网络错误时的重试机制
- 最终检查失败的错误处理
- 更友好的错误提示信息

```typescript
try {
  initialBalance = await this.solanaService.getSolBalance();
  console.log(`📊 初始 Solana SOL 余额: ${initialBalance} SOL`);
} catch (error) {
  console.error("获取初始 Solana 余额失败:", error);
  throw new Error("无法获取 Solana 余额，请检查网络连接");
}
```

### 5. 优化日志输出频率

为了避免过多的日志输出，在 IoTeX SOL 余额检测中添加了日志频率控制：

```typescript
// 每 3 次检查才输出一次日志，避免过多输出
if (checkCount % 3 === 1) {
  console.log(`🔍 检查 ${checkCount} (${elapsedTime}s): 当前余额 ${currentBalance} SOL`);
}
```

## 修复效果

### 1. 解决超时问题
- 正确的等待时间：从 0.6 秒修复为 10 分钟
- 合理的检查间隔：15 秒，适合跨链转账的确认时间

### 2. 提供详细的执行信息
- 显示初始余额和目标余额
- 实时显示检查进度和剩余时间
- 提供最终的余额变化统计

### 3. 更好的用户体验
- 清楚地了解等待过程
- 网络错误时的友好提示
- 超时时的建议操作

### 4. 更强的容错能力
- 网络异常时自动重试
- 详细的错误信息帮助调试
- 部分到账时的智能处理

## 示例输出

修复后的日志输出示例：

```
⏳ 等待 Solana SOL 余额增加 0.112055628204 SOL...
📊 初始 Solana SOL 余额: 0.5 SOL
🎯 目标余额: 0.606453 SOL (需增加 0.106453 SOL)
🔍 第 1 次检查 (15s): 当前余额 0.5 SOL
⏳ 余额未达到目标，15 秒后重新检查 (剩余 585s)
🔍 第 4 次检查 (60s): 当前余额 0.612 SOL
✅ Solana SOL 余额已增加 0.112000 SOL
```

## 预防措施

为了避免类似问题，建议：

1. **参数验证**: 在方法开始时验证参数的合理性
2. **单位统一**: 所有时间参数统一使用毫秒
3. **默认值合理**: 设置合理的默认等待时间
4. **充分测试**: 在测试环境中验证等待逻辑
5. **文档完善**: 清楚地说明参数的单位和含义

## 总结

这次修复解决了余额检测超时的根本问题，提供了更好的用户体验和调试能力。用户现在可以清楚地看到等待过程，了解转账的进度，并在出现问题时获得有用的诊断信息。
