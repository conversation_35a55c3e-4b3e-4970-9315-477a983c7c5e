# 钱包生成器使用说明

## 概述

`generate-wallets.ts` 脚本可以自动生成 IoTeX 和 Solana 钱包地址和私钥，用于套利系统或其他用途。

## 功能特性

- ✅ 生成 IoTeX 钱包（地址、私钥、公钥）
- ✅ 生成 Solana 钱包（地址、私钥、公钥）
- ✅ 支持多种私钥格式（十六进制、Base58）
- ✅ 保存到 JSON 文件
- ✅ 生成环境变量配置文件
- ✅ 安全的随机数生成

## 使用方法

### 1. 基本使用（仅显示）

```bash
npm run generate-wallets
```

输出示例：
```
🔐 开始生成钱包...

✅ IoTeX 钱包生成成功
✅ Solana 钱包生成成功

📋 生成的钱包信息:
================================================================================

🌐 IoTeX 钱包:
   地址:     io1abc123...
   私钥:     1234567890abcdef...
   公钥:     04abcdef...

☀️  Solana 钱包:
   地址:     9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
   私钥(Hex): 1234567890abcdef...
   私钥(B58): 5Kj8R2wDVRWMszaN6iBL2rHRQuD7T6CG7wkzds8SjxRqg...
   公钥:     9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM

================================================================================
```

### 2. 保存到文件

```bash
npm run generate-wallets-save
```

或者：

```bash
npm run generate-wallets -- --save --env
```

这会生成两个文件：
- `generated-wallets/wallet-2024-01-01T12-00-00-000Z.json` - 钱包详细信息
- `generated-wallets/.env.wallet-2024-01-01T12-00-00-000Z` - 环境变量配置

### 3. 命令行参数

| 参数 | 简写 | 说明 |
|------|------|------|
| `--save` | `-s` | 保存钱包信息到 JSON 文件 |
| `--env` | `-e` | 生成环境变量配置文件 |
| `--filename=name` | - | 指定自定义文件名 |

### 4. 高级用法

```bash
# 只保存 JSON 文件
npm run generate-wallets -- --save

# 只生成环境变量文件
npm run generate-wallets -- --env

# 指定自定义文件名
npm run generate-wallets -- --save --filename=my-wallet.json
```

## 输出文件格式

### JSON 文件格式

```json
{
  "generated_at": "2024-01-01T12:00:00.000Z",
  "iotex": {
    "address": "io1abc123...",
    "private_key": "1234567890abcdef...",
    "public_key": "04abcdef..."
  },
  "solana": {
    "address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
    "private_key_hex": "1234567890abcdef...",
    "private_key_base58": "5Kj8R2wDVRWMszaN6iBL2rHRQuD7T6CG7wkzds8SjxRqg...",
    "public_key": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
  },
  "usage_notes": {
    "iotex_private_key": "用于 IoTeX 网络交易签名",
    "solana_private_key_hex": "十六进制格式，用于某些工具",
    "solana_private_key_base58": "Base58 格式，Solana 官方推荐格式"
  }
}
```

### 环境变量文件格式

```bash
# IoTeX 钱包配置
IOTEX_PRIVATE_KEY=1234567890abcdef...
IOTEX_WALLET_ADDRESS=io1abc123...

# Solana 钱包配置
SOLANA_PRIVATE_KEY=5Kj8R2wDVRWMszaN6iBL2rHRQuD7T6CG7wkzds8SjxRqg...
SOLANA_WALLET_ADDRESS=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM

# 套利系统配置示例
ARBITRAGE_IOTEX_ADDRESS=io1abc123...
ARBITRAGE_SOLANA_ADDRESS=9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
```

## 安全注意事项

### ⚠️ 重要安全提醒

1. **私钥保护**：
   - 生成的私钥具有完全的资金控制权
   - 请妥善保管，不要泄露给任何人
   - 不要在公共场所或不安全的网络环境中生成

2. **文件安全**：
   - 生成的文件包含敏感信息
   - 建议加密存储或使用安全的密码管理器
   - 不要将包含私钥的文件上传到版本控制系统

3. **测试验证**：
   - 在生产环境使用前，请先在测试网络验证
   - 建议先用小额资金测试

4. **备份策略**：
   - 建议创建多个备份副本
   - 存储在不同的安全位置
   - 考虑使用硬件钱包作为冷存储

## 集成到套利系统

### 1. 使用环境变量

将生成的环境变量文件重命名为 `.env` 并放在项目根目录：

```bash
cp generated-wallets/.env.wallet-xxx .env
```

### 2. 在代码中使用

```typescript
// 在套利系统中使用
const config = {
  arbitrage: {
    walletAddresses: {
      iotex: process.env.IOTEX_WALLET_ADDRESS,
      solana: process.env.SOLANA_WALLET_ADDRESS,
    },
  },
  mimo: {
    privateKey: process.env.IOTEX_PRIVATE_KEY,
  },
  solana: {
    privateKey: process.env.SOLANA_PRIVATE_KEY,
  },
};
```

## 故障排除

### 常见问题

1. **权限错误**：
   ```bash
   chmod +x scripts/generate-wallets.ts
   ```

2. **依赖缺失**：
   ```bash
   npm install
   ```

3. **TypeScript 错误**：
   ```bash
   npm install -g ts-node typescript
   ```

### 验证生成的钱包

#### IoTeX 钱包验证

```bash
# 使用 iotex-core 客户端验证地址格式
echo "io1abc123..." | iotex account validate
```

#### Solana 钱包验证

```bash
# 使用 Solana CLI 验证
solana-keygen verify <public-key> <private-key-file>
```

## 开发说明

### 技术实现

- **IoTeX 钱包**：使用 `@iotexproject/iotex-address-ts` 库
- **Solana 钱包**：使用 `@solana/web3.js` 库
- **随机数生成**：使用 `crypto.getRandomValues()` 确保安全性
- **私钥格式**：支持十六进制和 Base58 格式

### 扩展功能

可以根据需要扩展以下功能：

1. **批量生成**：一次生成多个钱包
2. **助记词支持**：支持 BIP39 助记词
3. **硬件钱包集成**：支持 Ledger/Trezor
4. **多网络支持**：支持更多区块链网络

## 许可证

本脚本遵循项目的开源许可证。请确保在使用时遵守相关法律法规。
